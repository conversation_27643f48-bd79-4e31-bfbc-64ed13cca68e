package com.dars.language.dars_language

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine

class MainActivity : FlutterActivity() {
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Register native plugins
        flutterEngine.plugins.add(MediaControlPlugin())
        flutterEngine.plugins.add(SystemControlPlugin())
        flutterEngine.plugins.add(NotificationPlugin())
    }
}
