package com.dars.language.dars_language

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result

class NotificationPlugin: FlutterPlugin, MethodCallHandler {
    private lateinit var channel: MethodChannel
    private lateinit var context: Context

    companion object {
        const val CHANNEL_ID = "DARS_FOREGROUND_SERVICE"
        const val NOTIFICATION_ID = 1001
        var notificationManager: NotificationManager? = null
    }

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "flutter/notification_service")
        channel.setMethodCallHandler(this)
        context = flutterPluginBinding.applicationContext
        
        // Créer le canal de notification
        createNotificationChannel()
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "startForegroundService" -> {
                startForegroundService()
                result.success(null)
            }
            "stopForegroundService" -> {
                stopForegroundService()
                result.success(null)
            }
            "updateNotificationStatus" -> {
                val status = call.argument<String>("status") ?: "Active"
                updateNotificationStatus(status)
                result.success(null)
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "DARS Language Service",
                NotificationManager.IMPORTANCE_MIN  // IMPORTANCE_MIN pour icône persistante
            ).apply {
                description = "Service de contrôle gestuel DARS Language"
                setShowBadge(false)
                setSound(null, null)  // Pas de son
                enableLights(false)   // Pas de lumière
                enableVibration(false) // Pas de vibration
            }

            notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager?.createNotificationChannel(channel)
        }
    }

    private fun startForegroundService() {
        val intent = Intent(context, DarsLanguageService::class.java)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent)
        } else {
            context.startService(intent)
        }
    }

    private fun stopForegroundService() {
        val intent = Intent(context, DarsLanguageService::class.java)
        context.stopService(intent)
    }

    private fun updateNotificationStatus(status: String) {
        DarsLanguageService.updateNotification(context, status)
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
    }
}

class DarsLanguageService : Service() {
    
    companion object {
        fun updateNotification(context: Context, status: String) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            val notification = createNotification(context, status)
            notificationManager.notify(NotificationPlugin.NOTIFICATION_ID, notification)
        }
        
        private fun createNotification(context: Context, status: String): Notification {
            val intent = context.packageManager.getLaunchIntentForPackage(context.packageName)
            val pendingIntent = PendingIntent.getActivity(
                context, 0, intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            return NotificationCompat.Builder(context, NotificationPlugin.CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_dars_notification) // Icône DARS dans la barre de statut
                .setContentTitle("DARS")
                .setContentText(status)
                .setContentIntent(pendingIntent)
                .setOngoing(true) // Notification persistante
                .setPriority(NotificationCompat.PRIORITY_MIN) // Priorité minimale pour rester discrète
                .setCategory(NotificationCompat.CATEGORY_SERVICE)
                .setShowWhen(false) // Pas d'heure affichée
                .setSound(null) // Pas de son
                .setVibrate(null) // Pas de vibration
                .setLights(0, 0, 0) // Pas de lumière
                .setVisibility(NotificationCompat.VISIBILITY_SECRET) // Masquer sur écran de verrouillage
                .build()
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val notification = createNotification(this, "Actif")
        startForeground(NotificationPlugin.NOTIFICATION_ID, notification)
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null
}
