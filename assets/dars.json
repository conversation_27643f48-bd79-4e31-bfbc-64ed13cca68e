{"version": "1.0", "locale_default": "en", "profiles": ["global", "media", "presentation", "driving"], "rules": [{"name": "PlayPause_Global", "profile": "global", "trigger": {"type": "symbol", "symbol": "B"}, "conditions": {"ready": true, "min_confidence": 0.7}, "action": {"type": "media", "name": "play_pause"}, "cooldown_ms": 500, "enabled": true}, {"name": "NextTrack_Global", "profile": "global", "trigger": {"type": "symbol", "symbol": "D"}, "conditions": {"ready": true, "min_confidence": 0.75}, "action": {"type": "media", "name": "next_track"}, "cooldown_ms": 600, "enabled": true}, {"name": "VolumeUp_Mod", "profile": "global", "trigger": {"type": "modifier_symbol", "modifier": "L", "symbol": "R"}, "conditions": {"ready": true, "min_confidence": 0.7}, "action": {"type": "system", "name": "volume_up", "params": {"steps": 1}}, "cooldown_ms": 400, "enabled": true}, {"name": "VolumeDown_Mod", "profile": "global", "trigger": {"type": "modifier_symbol", "modifier": "L", "symbol": "Lr"}, "conditions": {"ready": true, "min_confidence": 0.7}, "action": {"type": "system", "name": "volume_down", "params": {"steps": 1}}, "cooldown_ms": 400, "enabled": true}, {"name": "OpenCamera_Global", "profile": "global", "trigger": {"type": "symbol", "symbol": "O"}, "conditions": {"ready": true, "min_confidence": 0.7}, "action": {"type": "camera", "name": "open_camera", "params": {"mode": "photo"}}, "cooldown_ms": 800, "enabled": true}, {"name": "SwitchLens_Mod", "profile": "global", "trigger": {"type": "modifier_symbol", "modifier": "L", "symbol": "O"}, "conditions": {"ready": true, "min_confidence": 0.75}, "action": {"type": "camera", "name": "switch_camera", "params": {"to": "front_back_toggle"}}, "cooldown_ms": 800, "enabled": true}, {"name": "Back_Cancel", "profile": "global", "trigger": {"type": "symbol", "symbol": "N"}, "conditions": {"ready": true, "min_confidence": 0.7}, "action": {"type": "navigation", "name": "back"}, "cooldown_ms": 500, "enabled": true}, {"name": "Confirm_OK", "profile": "global", "trigger": {"type": "symbol", "symbol": "Y"}, "conditions": {"ready": true, "min_confidence": 0.7}, "action": {"type": "navigation", "name": "confirm"}, "cooldown_ms": 500, "enabled": true}, {"name": "Mute_Audio", "profile": "global", "trigger": {"type": "symbol", "symbol": "P"}, "conditions": {"ready": true, "min_confidence": 0.75}, "action": {"type": "media", "name": "mute_toggle"}, "cooldown_ms": 600, "enabled": true}, {"name": "Screenshot_Mod", "profile": "global", "trigger": {"type": "modifier_symbol", "modifier": "L", "symbol": "B"}, "conditions": {"ready": true, "min_confidence": 0.8}, "action": {"type": "system", "name": "screenshot"}, "cooldown_ms": 1200, "enabled": true}, {"name": "LaunchNotes_Sequence", "profile": "global", "trigger": {"type": "sequence", "sequence": ["D", "S"], "window_ms": 1800}, "conditions": {"ready": true, "min_confidence": 0.75}, "action": {"type": "app", "name": "launch_app", "params": {"bundle": "com.example.notes"}}, "cooldown_ms": 1000, "enabled": true}, {"name": "LockScreen_LongEyes", "profile": "global", "trigger": {"type": "symbol", "symbol": "F"}, "conditions": {"ready": true, "min_confidence": 0.85}, "action": {"type": "system", "name": "lock_screen"}, "cooldown_ms": 1500, "enabled": true}, {"name": "SOS_Mod_LongEyes", "profile": "global", "trigger": {"type": "modifier_symbol", "modifier": "L", "symbol": "F"}, "conditions": {"ready": true, "min_confidence": 0.9}, "action": {"type": "macro", "name": "sos_sequence", "params": {"steps": [{"type": "system", "name": "enable_gps"}, {"type": "system", "name": "send_location", "params": {"to": "ICE_CONTACT"}}, {"type": "system", "name": "start_emergency_call", "params": {"number": "112"}}]}}, "cooldown_ms": 5000, "enabled": false}, {"name": "Media_PlayPause", "profile": "media", "trigger": {"type": "symbol", "symbol": "B"}, "conditions": {"ready": true, "min_confidence": 0.7}, "action": {"type": "media", "name": "play_pause"}, "cooldown_ms": 400, "enabled": true}, {"name": "Media_NextPrev", "profile": "media", "trigger": {"type": "symbol", "symbol": "R"}, "conditions": {"ready": true, "min_confidence": 0.7}, "action": {"type": "media", "name": "next_track"}, "cooldown_ms": 400, "enabled": true}, {"name": "Media_Prev", "profile": "media", "trigger": {"type": "symbol", "symbol": "Lr"}, "conditions": {"ready": true, "min_confidence": 0.7}, "action": {"type": "media", "name": "previous_track"}, "cooldown_ms": 400, "enabled": true}, {"name": "Slides_Next", "profile": "presentation", "trigger": {"type": "symbol", "symbol": "R"}, "conditions": {"ready": true, "min_confidence": 0.7}, "action": {"type": "navigation", "name": "next_slide"}, "cooldown_ms": 300, "enabled": true}, {"name": "Slides_Prev", "profile": "presentation", "trigger": {"type": "symbol", "symbol": "Lr"}, "conditions": {"ready": true, "min_confidence": 0.7}, "action": {"type": "navigation", "name": "previous_slide"}, "cooldown_ms": 300, "enabled": true}, {"name": "Slides_Laser_Mod", "profile": "presentation", "trigger": {"type": "modifier_symbol", "modifier": "L", "symbol": "S"}, "conditions": {"ready": true, "min_confidence": 0.75}, "action": {"type": "app", "name": "toggle_pointer"}, "cooldown_ms": 600, "enabled": true}, {"name": "Driving_AnswerCall", "profile": "driving", "trigger": {"type": "symbol", "symbol": "Y"}, "conditions": {"ready": true, "min_confidence": 0.8}, "action": {"type": "telephony", "name": "answer_call"}, "cooldown_ms": 1000, "enabled": true}, {"name": "Driving_RejectCall", "profile": "driving", "trigger": {"type": "symbol", "symbol": "N"}, "conditions": {"ready": true, "min_confidence": 0.8}, "action": {"type": "telephony", "name": "reject_call"}, "cooldown_ms": 1000, "enabled": true}]}