import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/app_providers.dart';
import '../models/gesture.dart';
// TODO: Add localization support later

class GestureHistoryWidget extends ConsumerWidget {
  const GestureHistoryWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final gestureHistory = ref.watch(gestureHistoryProvider);

    if (gestureHistory.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 60,
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Gesture History',
            style: TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: gestureHistory.length,
              itemBuilder: (context, index) {
                final gesture = gestureHistory[index];
                final age = DateTime.now().millisecondsSinceEpoch - gesture.timestamp;
                final opacity = (2000 - age.clamp(0, 2000)) / 2000.0;
                
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: Opacity(
                    opacity: opacity.clamp(0.3, 1.0),
                    child: _buildGestureItem(gesture),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGestureItem(DetectedGesture gesture) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: _getGestureColor(gesture.type),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Center(
        child: Text(
          gesture.symbol,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Color _getGestureColor(GestureType type) {
    switch (type) {
      case GestureType.blink:
      case GestureType.doubleBlink:
      case GestureType.longBlink:
        return Colors.blue;
      case GestureType.mouthOpen:
      case GestureType.smile:
      case GestureType.lipsPucker:
        return Colors.green;
      case GestureType.headTiltRight:
      case GestureType.headTiltLeft:
      case GestureType.headNodYes:
      case GestureType.headShakeNo:
        return Colors.orange;
    }
  }
}
