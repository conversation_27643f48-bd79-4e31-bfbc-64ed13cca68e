import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/app_providers.dart';
import '../models/gesture.dart';
// TODO: Add localization support later

class GestureHUD extends ConsumerWidget {
  const GestureHUD({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final gestureStream = ref.watch(gestureStreamProvider);
    final faceDetectedStream = ref.watch(faceDetectedStreamProvider);
    final metricsStream = ref.watch(metricsStreamProvider);

    return Stack(
      children: [
        // Face detection indicator
        Positioned(
          top: 60,
          left: 16,
          child: _buildFaceDetectionIndicator(faceDetectedStream),
        ),

        // Current gesture display - SUPPRIMÉ
        
        // Metrics display
        Positioned(
          top: 100,
          left: 16,
          child: _buildMetricsDisplay(metricsStream),
        ),
        
        // Face landmarks overlay (for debugging)
        if (_shouldShowLandmarks())
          Positioned.fill(
            child: _buildLandmarksOverlay(metricsStream),
          ),
      ],
    );
  }

  Widget _buildFaceDetectionIndicator(AsyncValue<bool> faceDetectedStream) {
    return faceDetectedStream.when(
      data: (faceDetected) => Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: faceDetected ? Colors.green.withValues(alpha: 0.8) : Colors.red.withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              faceDetected ? Icons.face : Icons.face_retouching_off,
              color: Colors.white,
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              faceDetected ? 'Face detected' : 'Face not detected',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  Widget _buildCurrentGestureDisplay(AsyncValue<DetectedGesture> gestureStream) {
    return gestureStream.when(
      data: (gesture) => TweenAnimationBuilder<double>(
        duration: const Duration(milliseconds: 300),
        tween: Tween(begin: 0.0, end: 1.0),
        builder: (context, value, child) {
          return Transform.scale(
            scale: 0.8 + (0.2 * value),
            child: Opacity(
              opacity: value,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Center(
                            child: Text(
                              gesture.symbol,
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Gesture: ${_getGestureDisplayName(gesture.type)}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                'Confidence: ${(gesture.confidence * 100).round()}%',
                                style: const TextStyle(
                                  color: Colors.white70,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  Widget _buildMetricsDisplay(AsyncValue<GestureMetrics> metricsStream) {
    return metricsStream.when(
      data: (metrics) => Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Metrics',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'EAR: ${metrics.ear.toStringAsFixed(3)}',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 12,
                fontFamily: 'monospace',
              ),
            ),
            Text(
              'MAR: ${metrics.mar.toStringAsFixed(3)}',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 12,
                fontFamily: 'monospace',
              ),
            ),
            Text(
              'Yaw: ${metrics.headPose.yaw.toStringAsFixed(1)}°',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 12,
                fontFamily: 'monospace',
              ),
            ),
            Text(
              'Pitch: ${metrics.headPose.pitch.toStringAsFixed(1)}°',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 12,
                fontFamily: 'monospace',
              ),
            ),
            Text(
              'Roll: ${metrics.headPose.roll.toStringAsFixed(1)}°',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 12,
                fontFamily: 'monospace',
              ),
            ),
          ],
        ),
      ),
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  Widget _buildLandmarksOverlay(AsyncValue<GestureMetrics> metricsStream) {
    return metricsStream.when(
      data: (metrics) => CustomPaint(
        painter: LandmarksPainter(metrics),
        size: Size.infinite,
      ),
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  bool _shouldShowLandmarks() {
    // Only show landmarks in debug mode or when explicitly enabled
    return false; // Set to true for debugging
  }

  String _getGestureDisplayName(GestureType type) {
    switch (type) {
      case GestureType.blink:
        return 'Blink';
      case GestureType.doubleBlink:
        return 'Double Blink';
      case GestureType.longBlink:
        return 'Long Blink';
      case GestureType.mouthOpen:
        return 'Mouth Open';
      case GestureType.smile:
        return 'Smile';
      case GestureType.lipsPucker:
        return 'Lips Pucker';
      case GestureType.headTiltRight:
        return 'Head Tilt Right';
      case GestureType.headTiltLeft:
        return 'Head Tilt Left';
      case GestureType.headNodYes:
        return 'Head Nod Yes';
      case GestureType.headShakeNo:
        return 'Head Shake No';
    }
  }
}

class LandmarksPainter extends CustomPainter {
  final GestureMetrics metrics;

  LandmarksPainter(this.metrics);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.green
      ..strokeWidth = 2.0
      ..style = PaintingStyle.fill;

    // This is a simplified visualization
    // In a real implementation, you would draw the actual face landmarks
    
    // Draw a simple face outline as placeholder
    final center = Offset(size.width / 2, size.height / 2);
    final faceRadius = size.width * 0.15;
    
    // Face outline
    paint.style = PaintingStyle.stroke;
    paint.color = Colors.green.withValues(alpha: 0.5);
    canvas.drawCircle(center, faceRadius, paint);
    
    // Eyes
    paint.style = PaintingStyle.fill;
    paint.color = Colors.blue.withValues(alpha: 0.7);
    final eyeRadius = 4.0;
    canvas.drawCircle(
      center + Offset(-faceRadius * 0.3, -faceRadius * 0.2),
      eyeRadius,
      paint,
    );
    canvas.drawCircle(
      center + Offset(faceRadius * 0.3, -faceRadius * 0.2),
      eyeRadius,
      paint,
    );
    
    // Mouth
    paint.color = Colors.red.withValues(alpha: 0.7);
    canvas.drawCircle(
      center + Offset(0, faceRadius * 0.3),
      6.0,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
