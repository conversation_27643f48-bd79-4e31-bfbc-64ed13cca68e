import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/app_providers.dart';

class ConfidenceIndicator extends ConsumerWidget {
  const ConfidenceIndicator({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final metricsStream = ref.watch(metricsStreamProvider);

    return metricsStream.when(
      data: (metrics) => Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Confidence bar
            Container(
              width: 60,
              height: 8,
              decoration: BoxDecoration(
                color: Colors.grey[800],
                borderRadius: BorderRadius.circular(4),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: metrics.confidence,
                child: Container(
                  decoration: BoxDecoration(
                    color: _getConfidenceColor(metrics.confidence),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '${(metrics.confidence * 100).round()}%',
              style: TextStyle(
                color: _getConfidenceColor(metrics.confidence),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  Color _getConfidenceColor(double confidence) {
    if (confidence >= 0.8) return Colors.green;
    if (confidence >= 0.6) return Colors.orange;
    return Colors.red;
  }
}
