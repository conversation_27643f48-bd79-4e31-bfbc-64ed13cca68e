{"@@locale": "ja", "appTitle": "DARS言語", "@appTitle": {"description": "The title of the application"}, "cameraView": "カメラビュー", "@cameraView": {"description": "Camera view screen title"}, "settings": "設定", "@settings": {"description": "Settings screen title"}, "ruleEditor": "ルールエディター", "@ruleEditor": {"description": "Rule editor screen title"}, "calibration": "キャリブレーション", "@calibration": {"description": "Calibration screen title"}, "profiles": "プロファイル", "@profiles": {"description": "Profiles screen title"}, "global": "グローバル", "@global": {"description": "Global profile name"}, "media": "メディア", "@media": {"description": "Media profile name"}, "presentation": "プレゼンテーション", "@presentation": {"description": "Presentation profile name"}, "driving": "運転", "@driving": {"description": "Driving profile name"}, "gestureDetected": "ジェスチャーが検出されました：{gesture}", "@gestureDetected": {"description": "Message when a gesture is detected", "placeholders": {"gesture": {"type": "String", "example": "まばたき"}}}, "confidence": "信頼度：{confidence}%", "@confidence": {"description": "Confidence level display", "placeholders": {"confidence": {"type": "int", "example": "85"}}}, "startCalibration": "キャリブレーション開始", "@startCalibration": {"description": "Button to start calibration"}, "calibrationComplete": "キャリブレーション完了", "@calibrationComplete": {"description": "Message when calibration is finished"}, "enableRule": "ルールを有効にする", "@enableRule": {"description": "Button to enable a rule"}, "disableRule": "ルールを無効にする", "@disableRule": {"description": "Button to disable a rule"}, "addRule": "ルールを追加", "@addRule": {"description": "Button to add a new rule"}, "editRule": "ルールを編集", "@editRule": {"description": "<PERSON><PERSON> to edit an existing rule"}, "deleteRule": "ルールを削除", "@deleteRule": {"description": "Button to delete a rule"}, "ruleName": "ルール名", "@ruleName": {"description": "Label for rule name field"}, "trigger": "トリガー", "@trigger": {"description": "Label for trigger configuration"}, "action": "アクション", "@action": {"description": "Label for action configuration"}, "cooldown": "クールダウン（ミリ秒）", "@cooldown": {"description": "Label for cooldown setting"}, "minConfidence": "最小信頼度", "@minConfidence": {"description": "Label for minimum confidence setting"}, "save": "保存", "@save": {"description": "Save button"}, "cancel": "キャンセル", "@cancel": {"description": "Cancel button"}, "confirm": "確認", "@confirm": {"description": "Confirm button"}, "back": "戻る", "@back": {"description": "Back button"}, "cameraPermissionRequired": "このアプリを使用するにはカメラの許可が必要です", "@cameraPermissionRequired": {"description": "Message when camera permission is needed"}, "grantPermission": "許可を与える", "@grantPermission": {"description": "Button to grant permission"}, "faceNotDetected": "顔が検出されませんでした", "@faceNotDetected": {"description": "Message when no face is detected"}, "faceDetected": "顔が検出されました", "@faceDetected": {"description": "Message when face is detected"}, "gestureHistory": "ジェスチャー履歴", "@gestureHistory": {"description": "Title for gesture history section"}, "exportRules": "ルールをエクスポート", "@exportRules": {"description": "Button to export rules"}, "importRules": "ルールをインポート", "@importRules": {"description": "Button to import rules"}, "language": "言語", "@language": {"description": "Language selection label"}, "sensitivity": "感度", "@sensitivity": {"description": "Sensitivity setting label"}, "safeMode": "セーフモード", "@safeMode": {"description": "Safe mode setting label"}, "safeModeDescription": "基本的なコマンドのみが許可されています", "@safeModeDescription": {"description": "Description of safe mode"}}