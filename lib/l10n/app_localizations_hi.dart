// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Hindi (`hi`).
class AppLocalizationsHi extends AppLocalizations {
  AppLocalizationsHi([String locale = 'hi']) : super(locale);

  @override
  String get appTitle => 'DARS भाषा';

  @override
  String get cameraView => 'कैमरा दृश्य';

  @override
  String get settings => 'सेटिंग्स';

  @override
  String get ruleEditor => 'नियम संपादक';

  @override
  String get calibration => 'अंशांकन';

  @override
  String get profiles => 'प्रोफाइल';

  @override
  String get global => 'वैश्विक';

  @override
  String get media => 'मीडिया';

  @override
  String get presentation => 'प्रस्तुति';

  @override
  String get driving => 'ड्राइविंग';

  @override
  String gestureDetected(String gesture) {
    return 'इशारा पहचाना गया: $gesture';
  }

  @override
  String confidence(int confidence) {
    return 'विश्वास: $confidence%';
  }

  @override
  String get startCalibration => 'अंशांकन शुरू करें';

  @override
  String get calibrationComplete => 'अंशांकन पूर्ण';

  @override
  String get enableRule => 'नियम सक्षम करें';

  @override
  String get disableRule => 'नियम अक्षम करें';

  @override
  String get addRule => 'नियम जोड़ें';

  @override
  String get editRule => 'नियम संपादित करें';

  @override
  String get deleteRule => 'नियम हटाएं';

  @override
  String get ruleName => 'नियम का नाम';

  @override
  String get trigger => 'ट्रिगर';

  @override
  String get action => 'कार्य';

  @override
  String get cooldown => 'कूलडाउन (मिलीसेकंड)';

  @override
  String get minConfidence => 'न्यूनतम विश्वास';

  @override
  String get save => 'सहेजें';

  @override
  String get cancel => 'रद्द करें';

  @override
  String get confirm => 'पुष्टि करें';

  @override
  String get back => 'वापस';

  @override
  String get cameraPermissionRequired =>
      'इस ऐप का उपयोग करने के लिए कैमरा अनुमति आवश्यक है';

  @override
  String get grantPermission => 'अनुमति दें';

  @override
  String get faceNotDetected => 'चेहरा नहीं मिला';

  @override
  String get faceDetected => 'चेहरा मिला';

  @override
  String get gestureHistory => 'इशारा इतिहास';

  @override
  String get exportRules => 'नियम निर्यात करें';

  @override
  String get importRules => 'नियम आयात करें';

  @override
  String get language => 'भाषा';

  @override
  String get sensitivity => 'संवेदनशीलता';

  @override
  String get safeMode => 'सुरक्षित मोड';

  @override
  String get safeModeDescription => 'केवल आवश्यक कमांड की अनुमति है';
}
