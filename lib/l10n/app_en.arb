{"@@locale": "en", "appTitle": "DARS Language", "@appTitle": {"description": "The title of the application"}, "cameraView": "Camera View", "@cameraView": {"description": "Camera view screen title"}, "settings": "Settings", "@settings": {"description": "Settings screen title"}, "ruleEditor": "Rule Editor", "@ruleEditor": {"description": "Rule editor screen title"}, "calibration": "Calibration", "@calibration": {"description": "Calibration screen title"}, "profiles": "Profiles", "@profiles": {"description": "Profiles screen title"}, "global": "Global", "@global": {"description": "Global profile name"}, "media": "Media", "@media": {"description": "Media profile name"}, "presentation": "Presentation", "@presentation": {"description": "Presentation profile name"}, "driving": "Driving", "@driving": {"description": "Driving profile name"}, "gestureDetected": "Gesture Detected: {gesture}", "@gestureDetected": {"description": "Message when a gesture is detected", "placeholders": {"gesture": {"type": "String", "example": "Blink"}}}, "confidence": "Confidence: {confidence}%", "@confidence": {"description": "Confidence level display", "placeholders": {"confidence": {"type": "int", "example": "85"}}}, "startCalibration": "Start Calibration", "@startCalibration": {"description": "Button to start calibration"}, "calibrationComplete": "Calibration Complete", "@calibrationComplete": {"description": "Message when calibration is finished"}, "enableRule": "Enable Rule", "@enableRule": {"description": "Button to enable a rule"}, "disableRule": "Disable Rule", "@disableRule": {"description": "Button to disable a rule"}, "addRule": "Add Rule", "@addRule": {"description": "Button to add a new rule"}, "editRule": "Edit Rule", "@editRule": {"description": "<PERSON><PERSON> to edit an existing rule"}, "deleteRule": "Delete Rule", "@deleteRule": {"description": "Button to delete a rule"}, "ruleName": "Rule Name", "@ruleName": {"description": "Label for rule name field"}, "trigger": "<PERSON><PERSON>", "@trigger": {"description": "Label for trigger configuration"}, "action": "Action", "@action": {"description": "Label for action configuration"}, "cooldown": "Cooldown (ms)", "@cooldown": {"description": "Label for cooldown setting"}, "minConfidence": "Minimum Confidence", "@minConfidence": {"description": "Label for minimum confidence setting"}, "save": "Save", "@save": {"description": "Save button"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button"}, "confirm": "Confirm", "@confirm": {"description": "Confirm button"}, "back": "Back", "@back": {"description": "Back button"}, "cameraPermissionRequired": "Camera permission is required to use this app", "@cameraPermissionRequired": {"description": "Message when camera permission is needed"}, "grantPermission": "Grant Permission", "@grantPermission": {"description": "Button to grant permission"}, "faceNotDetected": "Face not detected", "@faceNotDetected": {"description": "Message when no face is detected"}, "faceDetected": "Face detected", "@faceDetected": {"description": "Message when face is detected"}, "gestureHistory": "Gesture History", "@gestureHistory": {"description": "Title for gesture history section"}, "exportRules": "Export Rules", "@exportRules": {"description": "Button to export rules"}, "importRules": "Import Rules", "@importRules": {"description": "Button to import rules"}, "language": "Language", "@language": {"description": "Language selection label"}, "sensitivity": "Sensitivity", "@sensitivity": {"description": "Sensitivity setting label"}, "safeMode": "Safe Mode", "@safeMode": {"description": "Safe mode setting label"}, "safeModeDescription": "Only essential commands are allowed", "@safeModeDescription": {"description": "Description of safe mode"}}