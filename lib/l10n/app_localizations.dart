import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_bn.dart';
import 'app_localizations_en.dart';
import 'app_localizations_es.dart';
import 'app_localizations_fr.dart';
import 'app_localizations_hi.dart';
import 'app_localizations_ja.dart';
import 'app_localizations_pt.dart';
import 'app_localizations_ru.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('bn'),
    Locale('en'),
    Locale('es'),
    Locale('fr'),
    Locale('hi'),
    Locale('ja'),
    Locale('pt'),
    Locale('ru'),
    Locale('zh'),
  ];

  /// The title of the application
  ///
  /// In en, this message translates to:
  /// **'DARS Language'**
  String get appTitle;

  /// Camera view screen title
  ///
  /// In en, this message translates to:
  /// **'Camera View'**
  String get cameraView;

  /// Settings screen title
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// Rule editor screen title
  ///
  /// In en, this message translates to:
  /// **'Rule Editor'**
  String get ruleEditor;

  /// Calibration screen title
  ///
  /// In en, this message translates to:
  /// **'Calibration'**
  String get calibration;

  /// Profiles screen title
  ///
  /// In en, this message translates to:
  /// **'Profiles'**
  String get profiles;

  /// Global profile name
  ///
  /// In en, this message translates to:
  /// **'Global'**
  String get global;

  /// Media profile name
  ///
  /// In en, this message translates to:
  /// **'Media'**
  String get media;

  /// Presentation profile name
  ///
  /// In en, this message translates to:
  /// **'Presentation'**
  String get presentation;

  /// Driving profile name
  ///
  /// In en, this message translates to:
  /// **'Driving'**
  String get driving;

  /// Message when a gesture is detected
  ///
  /// In en, this message translates to:
  /// **'Gesture Detected: {gesture}'**
  String gestureDetected(String gesture);

  /// Confidence level display
  ///
  /// In en, this message translates to:
  /// **'Confidence: {confidence}%'**
  String confidence(int confidence);

  /// Button to start calibration
  ///
  /// In en, this message translates to:
  /// **'Start Calibration'**
  String get startCalibration;

  /// Message when calibration is finished
  ///
  /// In en, this message translates to:
  /// **'Calibration Complete'**
  String get calibrationComplete;

  /// Button to enable a rule
  ///
  /// In en, this message translates to:
  /// **'Enable Rule'**
  String get enableRule;

  /// Button to disable a rule
  ///
  /// In en, this message translates to:
  /// **'Disable Rule'**
  String get disableRule;

  /// Button to add a new rule
  ///
  /// In en, this message translates to:
  /// **'Add Rule'**
  String get addRule;

  /// Button to edit an existing rule
  ///
  /// In en, this message translates to:
  /// **'Edit Rule'**
  String get editRule;

  /// Button to delete a rule
  ///
  /// In en, this message translates to:
  /// **'Delete Rule'**
  String get deleteRule;

  /// Label for rule name field
  ///
  /// In en, this message translates to:
  /// **'Rule Name'**
  String get ruleName;

  /// Label for trigger configuration
  ///
  /// In en, this message translates to:
  /// **'Trigger'**
  String get trigger;

  /// Label for action configuration
  ///
  /// In en, this message translates to:
  /// **'Action'**
  String get action;

  /// Label for cooldown setting
  ///
  /// In en, this message translates to:
  /// **'Cooldown (ms)'**
  String get cooldown;

  /// Label for minimum confidence setting
  ///
  /// In en, this message translates to:
  /// **'Minimum Confidence'**
  String get minConfidence;

  /// Save button
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// Cancel button
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// Confirm button
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// Back button
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;

  /// Message when camera permission is needed
  ///
  /// In en, this message translates to:
  /// **'Camera permission is required to use this app'**
  String get cameraPermissionRequired;

  /// Button to grant permission
  ///
  /// In en, this message translates to:
  /// **'Grant Permission'**
  String get grantPermission;

  /// Message when no face is detected
  ///
  /// In en, this message translates to:
  /// **'Face not detected'**
  String get faceNotDetected;

  /// Message when face is detected
  ///
  /// In en, this message translates to:
  /// **'Face detected'**
  String get faceDetected;

  /// Title for gesture history section
  ///
  /// In en, this message translates to:
  /// **'Gesture History'**
  String get gestureHistory;

  /// Button to export rules
  ///
  /// In en, this message translates to:
  /// **'Export Rules'**
  String get exportRules;

  /// Button to import rules
  ///
  /// In en, this message translates to:
  /// **'Import Rules'**
  String get importRules;

  /// Language selection label
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// Sensitivity setting label
  ///
  /// In en, this message translates to:
  /// **'Sensitivity'**
  String get sensitivity;

  /// Safe mode setting label
  ///
  /// In en, this message translates to:
  /// **'Safe Mode'**
  String get safeMode;

  /// Description of safe mode
  ///
  /// In en, this message translates to:
  /// **'Only essential commands are allowed'**
  String get safeModeDescription;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>[
    'ar',
    'bn',
    'en',
    'es',
    'fr',
    'hi',
    'ja',
    'pt',
    'ru',
    'zh',
  ].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'bn':
      return AppLocalizationsBn();
    case 'en':
      return AppLocalizationsEn();
    case 'es':
      return AppLocalizationsEs();
    case 'fr':
      return AppLocalizationsFr();
    case 'hi':
      return AppLocalizationsHi();
    case 'ja':
      return AppLocalizationsJa();
    case 'pt':
      return AppLocalizationsPt();
    case 'ru':
      return AppLocalizationsRu();
    case 'zh':
      return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
