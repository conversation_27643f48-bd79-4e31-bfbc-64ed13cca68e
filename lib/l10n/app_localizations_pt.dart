// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Portuguese (`pt`).
class AppLocalizationsPt extends AppLocalizations {
  AppLocalizationsPt([String locale = 'pt']) : super(locale);

  @override
  String get appTitle => 'DARS Linguagem';

  @override
  String get cameraView => 'Visualização da Câmera';

  @override
  String get settings => 'Configurações';

  @override
  String get ruleEditor => 'Editor de Regras';

  @override
  String get calibration => 'Calibração';

  @override
  String get profiles => 'Perfis';

  @override
  String get global => 'Global';

  @override
  String get media => 'Mídia';

  @override
  String get presentation => 'Apresentação';

  @override
  String get driving => 'Condução';

  @override
  String gestureDetected(String gesture) {
    return 'Gesto Detectado: $gesture';
  }

  @override
  String confidence(int confidence) {
    return 'Confiança: $confidence%';
  }

  @override
  String get startCalibration => 'Iniciar Calibração';

  @override
  String get calibrationComplete => 'Calibração Completa';

  @override
  String get enableRule => 'Ativar Regra';

  @override
  String get disableRule => 'Desativar Regra';

  @override
  String get addRule => 'Adicionar Regra';

  @override
  String get editRule => 'Editar Regra';

  @override
  String get deleteRule => 'Excluir Regra';

  @override
  String get ruleName => 'Nome da Regra';

  @override
  String get trigger => 'Gatilho';

  @override
  String get action => 'Ação';

  @override
  String get cooldown => 'Tempo de Espera (ms)';

  @override
  String get minConfidence => 'Confiança Mínima';

  @override
  String get save => 'Salvar';

  @override
  String get cancel => 'Cancelar';

  @override
  String get confirm => 'Confirmar';

  @override
  String get back => 'Voltar';

  @override
  String get cameraPermissionRequired =>
      'Permissão da câmera é necessária para usar este aplicativo';

  @override
  String get grantPermission => 'Conceder Permissão';

  @override
  String get faceNotDetected => 'Rosto não detectado';

  @override
  String get faceDetected => 'Rosto detectado';

  @override
  String get gestureHistory => 'Histórico de Gestos';

  @override
  String get exportRules => 'Exportar Regras';

  @override
  String get importRules => 'Importar Regras';

  @override
  String get language => 'Idioma';

  @override
  String get sensitivity => 'Sensibilidade';

  @override
  String get safeMode => 'Modo Seguro';

  @override
  String get safeModeDescription => 'Apenas comandos essenciais são permitidos';
}
