// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'لغة DARS';

  @override
  String get cameraView => 'عرض الكاميرا';

  @override
  String get settings => 'الإعدادات';

  @override
  String get ruleEditor => 'محرر القواعد';

  @override
  String get calibration => 'المعايرة';

  @override
  String get profiles => 'الملفات الشخصية';

  @override
  String get global => 'عام';

  @override
  String get media => 'الوسائط';

  @override
  String get presentation => 'العرض التقديمي';

  @override
  String get driving => 'القيادة';

  @override
  String gestureDetected(String gesture) {
    return 'تم اكتشاف الإيماءة: $gesture';
  }

  @override
  String confidence(int confidence) {
    return 'الثقة: $confidence%';
  }

  @override
  String get startCalibration => 'بدء المعايرة';

  @override
  String get calibrationComplete => 'اكتملت المعايرة';

  @override
  String get enableRule => 'تفعيل القاعدة';

  @override
  String get disableRule => 'إلغاء تفعيل القاعدة';

  @override
  String get addRule => 'إضافة قاعدة';

  @override
  String get editRule => 'تحرير القاعدة';

  @override
  String get deleteRule => 'حذف القاعدة';

  @override
  String get ruleName => 'اسم القاعدة';

  @override
  String get trigger => 'المحفز';

  @override
  String get action => 'الإجراء';

  @override
  String get cooldown => 'فترة التهدئة (مللي ثانية)';

  @override
  String get minConfidence => 'الحد الأدنى للثقة';

  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get confirm => 'تأكيد';

  @override
  String get back => 'رجوع';

  @override
  String get cameraPermissionRequired =>
      'مطلوب إذن الكاميرا لاستخدام هذا التطبيق';

  @override
  String get grantPermission => 'منح الإذن';

  @override
  String get faceNotDetected => 'لم يتم اكتشاف الوجه';

  @override
  String get faceDetected => 'تم اكتشاف الوجه';

  @override
  String get gestureHistory => 'تاريخ الإيماءات';

  @override
  String get exportRules => 'تصدير القواعد';

  @override
  String get importRules => 'استيراد القواعد';

  @override
  String get language => 'اللغة';

  @override
  String get sensitivity => 'الحساسية';

  @override
  String get safeMode => 'الوضع الآمن';

  @override
  String get safeModeDescription => 'يُسمح بالأوامر الأساسية فقط';
}
