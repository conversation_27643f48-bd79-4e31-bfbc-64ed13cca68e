// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Russian (`ru`).
class AppLocalizationsRu extends AppLocalizations {
  AppLocalizationsRu([String locale = 'ru']) : super(locale);

  @override
  String get appTitle => 'DARS Язык';

  @override
  String get cameraView => 'Вид камеры';

  @override
  String get settings => 'Настройки';

  @override
  String get ruleEditor => 'Редактор правил';

  @override
  String get calibration => 'Калибровка';

  @override
  String get profiles => 'Профили';

  @override
  String get global => 'Глобальный';

  @override
  String get media => 'Медиа';

  @override
  String get presentation => 'Презентация';

  @override
  String get driving => 'Вождение';

  @override
  String gestureDetected(String gesture) {
    return 'Жест обнаружен: $gesture';
  }

  @override
  String confidence(int confidence) {
    return 'Уверенность: $confidence%';
  }

  @override
  String get startCalibration => 'Начать калибровку';

  @override
  String get calibrationComplete => 'Калибровка завершена';

  @override
  String get enableRule => 'Включить правило';

  @override
  String get disableRule => 'Отключить правило';

  @override
  String get addRule => 'Добавить правило';

  @override
  String get editRule => 'Редактировать правило';

  @override
  String get deleteRule => 'Удалить правило';

  @override
  String get ruleName => 'Название правила';

  @override
  String get trigger => 'Триггер';

  @override
  String get action => 'Действие';

  @override
  String get cooldown => 'Задержка (мс)';

  @override
  String get minConfidence => 'Минимальная уверенность';

  @override
  String get save => 'Сохранить';

  @override
  String get cancel => 'Отмена';

  @override
  String get confirm => 'Подтвердить';

  @override
  String get back => 'Назад';

  @override
  String get cameraPermissionRequired =>
      'Для использования этого приложения требуется разрешение камеры';

  @override
  String get grantPermission => 'Предоставить разрешение';

  @override
  String get faceNotDetected => 'Лицо не обнаружено';

  @override
  String get faceDetected => 'Лицо обнаружено';

  @override
  String get gestureHistory => 'История жестов';

  @override
  String get exportRules => 'Экспорт правил';

  @override
  String get importRules => 'Импорт правил';

  @override
  String get language => 'Язык';

  @override
  String get sensitivity => 'Чувствительность';

  @override
  String get safeMode => 'Безопасный режим';

  @override
  String get safeModeDescription => 'Разрешены только основные команды';
}
