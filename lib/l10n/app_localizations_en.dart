// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'DARS Language';

  @override
  String get cameraView => 'Camera View';

  @override
  String get settings => 'Settings';

  @override
  String get ruleEditor => 'Rule Editor';

  @override
  String get calibration => 'Calibration';

  @override
  String get profiles => 'Profiles';

  @override
  String get global => 'Global';

  @override
  String get media => 'Media';

  @override
  String get presentation => 'Presentation';

  @override
  String get driving => 'Driving';

  @override
  String gestureDetected(String gesture) {
    return 'Gesture Detected: $gesture';
  }

  @override
  String confidence(int confidence) {
    return 'Confidence: $confidence%';
  }

  @override
  String get startCalibration => 'Start Calibration';

  @override
  String get calibrationComplete => 'Calibration Complete';

  @override
  String get enableRule => 'Enable Rule';

  @override
  String get disableRule => 'Disable Rule';

  @override
  String get addRule => 'Add Rule';

  @override
  String get editRule => 'Edit Rule';

  @override
  String get deleteRule => 'Delete Rule';

  @override
  String get ruleName => 'Rule Name';

  @override
  String get trigger => 'Trigger';

  @override
  String get action => 'Action';

  @override
  String get cooldown => 'Cooldown (ms)';

  @override
  String get minConfidence => 'Minimum Confidence';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get confirm => 'Confirm';

  @override
  String get back => 'Back';

  @override
  String get cameraPermissionRequired =>
      'Camera permission is required to use this app';

  @override
  String get grantPermission => 'Grant Permission';

  @override
  String get faceNotDetected => 'Face not detected';

  @override
  String get faceDetected => 'Face detected';

  @override
  String get gestureHistory => 'Gesture History';

  @override
  String get exportRules => 'Export Rules';

  @override
  String get importRules => 'Import Rules';

  @override
  String get language => 'Language';

  @override
  String get sensitivity => 'Sensitivity';

  @override
  String get safeMode => 'Safe Mode';

  @override
  String get safeModeDescription => 'Only essential commands are allowed';
}
