{"@@locale": "ru", "appTitle": "DARS Язык", "@appTitle": {"description": "The title of the application"}, "cameraView": "<PERSON>ид камеры", "@cameraView": {"description": "Camera view screen title"}, "settings": "Настройки", "@settings": {"description": "Settings screen title"}, "ruleEditor": "Редактор правил", "@ruleEditor": {"description": "Rule editor screen title"}, "calibration": "Калибровка", "@calibration": {"description": "Calibration screen title"}, "profiles": "Профили", "@profiles": {"description": "Profiles screen title"}, "global": "Глобальный", "@global": {"description": "Global profile name"}, "media": "Медиа", "@media": {"description": "Media profile name"}, "presentation": "Презентация", "@presentation": {"description": "Presentation profile name"}, "driving": "Вождение", "@driving": {"description": "Driving profile name"}, "gestureDetected": "Жест обнаружен: {gesture}", "@gestureDetected": {"description": "Message when a gesture is detected", "placeholders": {"gesture": {"type": "String", "example": "Моргание"}}}, "confidence": "Уверенность: {confidence}%", "@confidence": {"description": "Confidence level display", "placeholders": {"confidence": {"type": "int", "example": "85"}}}, "startCalibration": "Начать калибровку", "@startCalibration": {"description": "Button to start calibration"}, "calibrationComplete": "Калибровка завершена", "@calibrationComplete": {"description": "Message when calibration is finished"}, "enableRule": "Включить правило", "@enableRule": {"description": "Button to enable a rule"}, "disableRule": "Отключить правило", "@disableRule": {"description": "Button to disable a rule"}, "addRule": "Добавить правило", "@addRule": {"description": "Button to add a new rule"}, "editRule": "Редактировать правило", "@editRule": {"description": "<PERSON><PERSON> to edit an existing rule"}, "deleteRule": "Удалить правило", "@deleteRule": {"description": "Button to delete a rule"}, "ruleName": "Название правила", "@ruleName": {"description": "Label for rule name field"}, "trigger": "Триггер", "@trigger": {"description": "Label for trigger configuration"}, "action": "Действие", "@action": {"description": "Label for action configuration"}, "cooldown": "Задержка (мс)", "@cooldown": {"description": "Label for cooldown setting"}, "minConfidence": "Минимальная уверенность", "@minConfidence": {"description": "Label for minimum confidence setting"}, "save": "Сохранить", "@save": {"description": "Save button"}, "cancel": "Отмена", "@cancel": {"description": "Cancel button"}, "confirm": "Подтвердить", "@confirm": {"description": "Confirm button"}, "back": "Назад", "@back": {"description": "Back button"}, "cameraPermissionRequired": "Для использования этого приложения требуется разрешение камеры", "@cameraPermissionRequired": {"description": "Message when camera permission is needed"}, "grantPermission": "Предоставить разрешение", "@grantPermission": {"description": "Button to grant permission"}, "faceNotDetected": "Лицо не обнаружено", "@faceNotDetected": {"description": "Message when no face is detected"}, "faceDetected": "Лицо обнаружено", "@faceDetected": {"description": "Message when face is detected"}, "gestureHistory": "История жестов", "@gestureHistory": {"description": "Title for gesture history section"}, "exportRules": "Экспорт правил", "@exportRules": {"description": "Button to export rules"}, "importRules": "Импорт правил", "@importRules": {"description": "Button to import rules"}, "language": "Язык", "@language": {"description": "Language selection label"}, "sensitivity": "Чувствительность", "@sensitivity": {"description": "Sensitivity setting label"}, "safeMode": "Безопасный режим", "@safeMode": {"description": "Safe mode setting label"}, "safeModeDescription": "Разрешены только основные команды", "@safeModeDescription": {"description": "Description of safe mode"}}