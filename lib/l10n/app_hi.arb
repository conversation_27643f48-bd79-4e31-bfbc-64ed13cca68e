{"@@locale": "hi", "appTitle": "DARS भाषा", "@appTitle": {"description": "The title of the application"}, "cameraView": "कैमरा दृश्य", "@cameraView": {"description": "Camera view screen title"}, "settings": "सेटिंग्स", "@settings": {"description": "Settings screen title"}, "ruleEditor": "नियम संपादक", "@ruleEditor": {"description": "Rule editor screen title"}, "calibration": "अंशांकन", "@calibration": {"description": "Calibration screen title"}, "profiles": "प्रोफाइल", "@profiles": {"description": "Profiles screen title"}, "global": "वैश्विक", "@global": {"description": "Global profile name"}, "media": "मीडिया", "@media": {"description": "Media profile name"}, "presentation": "प्रस्तुति", "@presentation": {"description": "Presentation profile name"}, "driving": "ड्राइविंग", "@driving": {"description": "Driving profile name"}, "gestureDetected": "इशारा पहचाना गया: {gesture}", "@gestureDetected": {"description": "Message when a gesture is detected", "placeholders": {"gesture": {"type": "String", "example": "पलक झपकना"}}}, "confidence": "विश्वास: {confidence}%", "@confidence": {"description": "Confidence level display", "placeholders": {"confidence": {"type": "int", "example": "85"}}}, "startCalibration": "अंशांकन शुरू करें", "@startCalibration": {"description": "Button to start calibration"}, "calibrationComplete": "अंशांकन पूर्ण", "@calibrationComplete": {"description": "Message when calibration is finished"}, "enableRule": "नियम सक्षम करें", "@enableRule": {"description": "Button to enable a rule"}, "disableRule": "नियम अक्षम करें", "@disableRule": {"description": "Button to disable a rule"}, "addRule": "नियम जोड़ें", "@addRule": {"description": "Button to add a new rule"}, "editRule": "नियम संपादित करें", "@editRule": {"description": "<PERSON><PERSON> to edit an existing rule"}, "deleteRule": "नियम हटाएं", "@deleteRule": {"description": "Button to delete a rule"}, "ruleName": "नियम का नाम", "@ruleName": {"description": "Label for rule name field"}, "trigger": "ट्रिगर", "@trigger": {"description": "Label for trigger configuration"}, "action": "कार्य", "@action": {"description": "Label for action configuration"}, "cooldown": "कूलडाउन (मिलीसेकंड)", "@cooldown": {"description": "Label for cooldown setting"}, "minConfidence": "न्यूनतम विश्वास", "@minConfidence": {"description": "Label for minimum confidence setting"}, "save": "सहेजें", "@save": {"description": "Save button"}, "cancel": "रद्<PERSON> करें", "@cancel": {"description": "Cancel button"}, "confirm": "पुष्टि करें", "@confirm": {"description": "Confirm button"}, "back": "वापस", "@back": {"description": "Back button"}, "cameraPermissionRequired": "इस ऐप का उपयोग करने के लिए कैमरा अनुमति आवश्यक है", "@cameraPermissionRequired": {"description": "Message when camera permission is needed"}, "grantPermission": "अनुमति दें", "@grantPermission": {"description": "Button to grant permission"}, "faceNotDetected": "चेहरा नहीं मिला", "@faceNotDetected": {"description": "Message when no face is detected"}, "faceDetected": "चेहरा मिला", "@faceDetected": {"description": "Message when face is detected"}, "gestureHistory": "इशारा इतिहास", "@gestureHistory": {"description": "Title for gesture history section"}, "exportRules": "नियम निर्यात करें", "@exportRules": {"description": "Button to export rules"}, "importRules": "नियम आयात करें", "@importRules": {"description": "Button to import rules"}, "language": "भाषा", "@language": {"description": "Language selection label"}, "sensitivity": "संवेदनशीलता", "@sensitivity": {"description": "Sensitivity setting label"}, "safeMode": "सुरक्षित मोड", "@safeMode": {"description": "Safe mode setting label"}, "safeModeDescription": "केवल आवश्यक कमांड की अनुमति है", "@safeModeDescription": {"description": "Description of safe mode"}}