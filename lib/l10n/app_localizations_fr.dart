// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appTitle => 'DARS Language';

  @override
  String get cameraView => 'Vue Caméra';

  @override
  String get settings => 'Paramètres';

  @override
  String get ruleEditor => 'Éditeur de Règles';

  @override
  String get calibration => 'Calibrage';

  @override
  String get profiles => 'Profils';

  @override
  String get global => 'Global';

  @override
  String get media => 'Média';

  @override
  String get presentation => 'Présentation';

  @override
  String get driving => 'Conduite';

  @override
  String gestureDetected(String gesture) {
    return 'Geste Détecté: $gesture';
  }

  @override
  String confidence(int confidence) {
    return 'Confiance: $confidence%';
  }

  @override
  String get startCalibration => 'Commencer le Calibrage';

  @override
  String get calibrationComplete => 'Calibrage Terminé';

  @override
  String get enableRule => 'Activer la Règle';

  @override
  String get disableRule => 'Désactiver la Règle';

  @override
  String get addRule => 'Ajouter une Règle';

  @override
  String get editRule => 'Modifier la Règle';

  @override
  String get deleteRule => 'Supprimer la Règle';

  @override
  String get ruleName => 'Nom de la Règle';

  @override
  String get trigger => 'Déclencheur';

  @override
  String get action => 'Action';

  @override
  String get cooldown => 'Délai (ms)';

  @override
  String get minConfidence => 'Confiance Minimale';

  @override
  String get save => 'Sauvegarder';

  @override
  String get cancel => 'Annuler';

  @override
  String get confirm => 'Confirmer';

  @override
  String get back => 'Retour';

  @override
  String get cameraPermissionRequired =>
      'L\'autorisation de la caméra est requise pour utiliser cette application';

  @override
  String get grantPermission => 'Accorder l\'Autorisation';

  @override
  String get faceNotDetected => 'Visage non détecté';

  @override
  String get faceDetected => 'Visage détecté';

  @override
  String get gestureHistory => 'Historique des Gestes';

  @override
  String get exportRules => 'Exporter les Règles';

  @override
  String get importRules => 'Importer les Règles';

  @override
  String get language => 'Langue';

  @override
  String get sensitivity => 'Sensibilité';

  @override
  String get safeMode => 'Mode Sécurisé';

  @override
  String get safeModeDescription =>
      'Seules les commandes essentielles sont autorisées';
}
