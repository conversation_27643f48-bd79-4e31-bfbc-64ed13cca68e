// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Japanese (`ja`).
class AppLocalizationsJa extends AppLocalizations {
  AppLocalizationsJa([String locale = 'ja']) : super(locale);

  @override
  String get appTitle => 'DARS言語';

  @override
  String get cameraView => 'カメラビュー';

  @override
  String get settings => '設定';

  @override
  String get ruleEditor => 'ルールエディター';

  @override
  String get calibration => 'キャリブレーション';

  @override
  String get profiles => 'プロファイル';

  @override
  String get global => 'グローバル';

  @override
  String get media => 'メディア';

  @override
  String get presentation => 'プレゼンテーション';

  @override
  String get driving => '運転';

  @override
  String gestureDetected(String gesture) {
    return 'ジェスチャーが検出されました：$gesture';
  }

  @override
  String confidence(int confidence) {
    return '信頼度：$confidence%';
  }

  @override
  String get startCalibration => 'キャリブレーション開始';

  @override
  String get calibrationComplete => 'キャリブレーション完了';

  @override
  String get enableRule => 'ルールを有効にする';

  @override
  String get disableRule => 'ルールを無効にする';

  @override
  String get addRule => 'ルールを追加';

  @override
  String get editRule => 'ルールを編集';

  @override
  String get deleteRule => 'ルールを削除';

  @override
  String get ruleName => 'ルール名';

  @override
  String get trigger => 'トリガー';

  @override
  String get action => 'アクション';

  @override
  String get cooldown => 'クールダウン（ミリ秒）';

  @override
  String get minConfidence => '最小信頼度';

  @override
  String get save => '保存';

  @override
  String get cancel => 'キャンセル';

  @override
  String get confirm => '確認';

  @override
  String get back => '戻る';

  @override
  String get cameraPermissionRequired => 'このアプリを使用するにはカメラの許可が必要です';

  @override
  String get grantPermission => '許可を与える';

  @override
  String get faceNotDetected => '顔が検出されませんでした';

  @override
  String get faceDetected => '顔が検出されました';

  @override
  String get gestureHistory => 'ジェスチャー履歴';

  @override
  String get exportRules => 'ルールをエクスポート';

  @override
  String get importRules => 'ルールをインポート';

  @override
  String get language => '言語';

  @override
  String get sensitivity => '感度';

  @override
  String get safeMode => 'セーフモード';

  @override
  String get safeModeDescription => '基本的なコマンドのみが許可されています';
}
