// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get appTitle => 'DARS Language';

  @override
  String get cameraView => 'Vista de Cámara';

  @override
  String get settings => 'Configuración';

  @override
  String get ruleEditor => 'Editor de Reglas';

  @override
  String get calibration => 'Calibración';

  @override
  String get profiles => 'Perfiles';

  @override
  String get global => 'Global';

  @override
  String get media => 'Medios';

  @override
  String get presentation => 'Presentación';

  @override
  String get driving => 'Conducción';

  @override
  String gestureDetected(String gesture) {
    return 'Gesto Detectado: $gesture';
  }

  @override
  String confidence(int confidence) {
    return 'Confianza: $confidence%';
  }

  @override
  String get startCalibration => 'Iniciar Calibración';

  @override
  String get calibrationComplete => 'Calibración Completa';

  @override
  String get enableRule => 'Habilitar Regla';

  @override
  String get disableRule => 'Deshabilitar Regla';

  @override
  String get addRule => 'Agregar Regla';

  @override
  String get editRule => 'Editar Regla';

  @override
  String get deleteRule => 'Eliminar Regla';

  @override
  String get ruleName => 'Nombre de la Regla';

  @override
  String get trigger => 'Disparador';

  @override
  String get action => 'Acción';

  @override
  String get cooldown => 'Enfriamiento (ms)';

  @override
  String get minConfidence => 'Confianza Mínima';

  @override
  String get save => 'Guardar';

  @override
  String get cancel => 'Cancelar';

  @override
  String get confirm => 'Confirmar';

  @override
  String get back => 'Atrás';

  @override
  String get cameraPermissionRequired =>
      'Se requiere permiso de cámara para usar esta aplicación';

  @override
  String get grantPermission => 'Conceder Permiso';

  @override
  String get faceNotDetected => 'Rostro no detectado';

  @override
  String get faceDetected => 'Rostro detectado';

  @override
  String get gestureHistory => 'Historial de Gestos';

  @override
  String get exportRules => 'Exportar Reglas';

  @override
  String get importRules => 'Importar Reglas';

  @override
  String get language => 'Idioma';

  @override
  String get sensitivity => 'Sensibilidad';

  @override
  String get safeMode => 'Modo Seguro';

  @override
  String get safeModeDescription => 'Solo se permiten comandos esenciales';
}
