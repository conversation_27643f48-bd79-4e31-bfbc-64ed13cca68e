{"@@locale": "pt", "appTitle": "DARS Linguagem", "@appTitle": {"description": "The title of the application"}, "cameraView": "Visualização da Câmera", "@cameraView": {"description": "Camera view screen title"}, "settings": "Configurações", "@settings": {"description": "Settings screen title"}, "ruleEditor": "Editor de Regras", "@ruleEditor": {"description": "Rule editor screen title"}, "calibration": "Calibração", "@calibration": {"description": "Calibration screen title"}, "profiles": "<PERSON><PERSON><PERSON>", "@profiles": {"description": "Profiles screen title"}, "global": "Global", "@global": {"description": "Global profile name"}, "media": "Mí<PERSON>", "@media": {"description": "Media profile name"}, "presentation": "Apresentação", "@presentation": {"description": "Presentation profile name"}, "driving": "Condução", "@driving": {"description": "Driving profile name"}, "gestureDetected": "<PERSON><PERSON><PERSON> Detectado: {gesture}", "@gestureDetected": {"description": "Message when a gesture is detected", "placeholders": {"gesture": {"type": "String", "example": "Piscar"}}}, "confidence": "Confiança: {confidence}%", "@confidence": {"description": "Confidence level display", "placeholders": {"confidence": {"type": "int", "example": "85"}}}, "startCalibration": "In<PERSON><PERSON>", "@startCalibration": {"description": "Button to start calibration"}, "calibrationComplete": "Calibração Completa", "@calibrationComplete": {"description": "Message when calibration is finished"}, "enableRule": "Ativar Regra", "@enableRule": {"description": "Button to enable a rule"}, "disableRule": "Desativar Regra", "@disableRule": {"description": "Button to disable a rule"}, "addRule": "Ad<PERSON><PERSON><PERSON>", "@addRule": {"description": "Button to add a new rule"}, "editRule": "<PERSON><PERSON>", "@editRule": {"description": "<PERSON><PERSON> to edit an existing rule"}, "deleteRule": "Excluir Regra", "@deleteRule": {"description": "Button to delete a rule"}, "ruleName": "Nome da Regra", "@ruleName": {"description": "Label for rule name field"}, "trigger": "<PERSON><PERSON><PERSON><PERSON>", "@trigger": {"description": "Label for trigger configuration"}, "action": "Ação", "@action": {"description": "Label for action configuration"}, "cooldown": "Tempo de Espera (ms)", "@cooldown": {"description": "Label for cooldown setting"}, "minConfidence": "Confian<PERSON>", "@minConfidence": {"description": "Label for minimum confidence setting"}, "save": "<PERSON><PERSON>", "@save": {"description": "Save button"}, "cancel": "<PERSON><PERSON><PERSON>", "@cancel": {"description": "Cancel button"}, "confirm": "Confirmar", "@confirm": {"description": "Confirm button"}, "back": "Voltar", "@back": {"description": "Back button"}, "cameraPermissionRequired": "Permissão da câmera é necessária para usar este aplicativo", "@cameraPermissionRequired": {"description": "Message when camera permission is needed"}, "grantPermission": "Conceder <PERSON>", "@grantPermission": {"description": "Button to grant permission"}, "faceNotDetected": "Rosto não detectado", "@faceNotDetected": {"description": "Message when no face is detected"}, "faceDetected": "Rosto detectado", "@faceDetected": {"description": "Message when face is detected"}, "gestureHistory": "Histórico de Gestos", "@gestureHistory": {"description": "Title for gesture history section"}, "exportRules": "Exportar Regras", "@exportRules": {"description": "Button to export rules"}, "importRules": "Importar Regras", "@importRules": {"description": "Button to import rules"}, "language": "Idioma", "@language": {"description": "Language selection label"}, "sensitivity": "Sensibilidade", "@sensitivity": {"description": "Sensitivity setting label"}, "safeMode": "<PERSON><PERSON>", "@safeMode": {"description": "Safe mode setting label"}, "safeModeDescription": "Apenas comandos essenciais são permitidos", "@safeModeDescription": {"description": "Description of safe mode"}}