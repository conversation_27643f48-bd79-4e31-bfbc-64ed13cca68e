// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Bengali Bangla (`bn`).
class AppLocalizationsBn extends AppLocalizations {
  AppLocalizationsBn([String locale = 'bn']) : super(locale);

  @override
  String get appTitle => 'DARS ভাষা';

  @override
  String get cameraView => 'ক্যামেরা দৃশ্য';

  @override
  String get settings => 'সেটিংস';

  @override
  String get ruleEditor => 'নিয়ম সম্পাদক';

  @override
  String get calibration => 'ক্যালিব্রেশন';

  @override
  String get profiles => 'প্রোফাইল';

  @override
  String get global => 'বিশ্বব্যাপী';

  @override
  String get media => 'মিডিয়া';

  @override
  String get presentation => 'উপস্থাপনা';

  @override
  String get driving => 'ড্রাইভিং';

  @override
  String gestureDetected(String gesture) {
    return 'অঙ্গভঙ্গি সনাক্ত করা হয়েছে: $gesture';
  }

  @override
  String confidence(int confidence) {
    return 'আস্থা: $confidence%';
  }

  @override
  String get startCalibration => 'ক্যালিব্রেশন শুরু করুন';

  @override
  String get calibrationComplete => 'ক্যালিব্রেশন সম্পূর্ণ';

  @override
  String get enableRule => 'নিয়ম সক্ষম করুন';

  @override
  String get disableRule => 'নিয়ম নিষ্ক্রিয় করুন';

  @override
  String get addRule => 'নিয়ম যোগ করুন';

  @override
  String get editRule => 'নিয়ম সম্পাদনা করুন';

  @override
  String get deleteRule => 'নিয়ম মুছুন';

  @override
  String get ruleName => 'নিয়মের নাম';

  @override
  String get trigger => 'ট্রিগার';

  @override
  String get action => 'কর্ম';

  @override
  String get cooldown => 'কুলডাউন (মিলিসেকেন্ড)';

  @override
  String get minConfidence => 'সর্বনিম্ন আস্থা';

  @override
  String get save => 'সংরক্ষণ করুন';

  @override
  String get cancel => 'বাতিল করুন';

  @override
  String get confirm => 'নিশ্চিত করুন';

  @override
  String get back => 'ফিরে যান';

  @override
  String get cameraPermissionRequired =>
      'এই অ্যাপ ব্যবহার করতে ক্যামেরার অনুমতি প্রয়োজন';

  @override
  String get grantPermission => 'অনুমতি দিন';

  @override
  String get faceNotDetected => 'মুখ সনাক্ত করা যায়নি';

  @override
  String get faceDetected => 'মুখ সনাক্ত করা হয়েছে';

  @override
  String get gestureHistory => 'অঙ্গভঙ্গির ইতিহাস';

  @override
  String get exportRules => 'নিয়ম রপ্তানি করুন';

  @override
  String get importRules => 'নিয়ম আমদানি করুন';

  @override
  String get language => 'ভাষা';

  @override
  String get sensitivity => 'সংবেদনশীলতা';

  @override
  String get safeMode => 'নিরাপদ মোড';

  @override
  String get safeModeDescription =>
      'শুধুমাত্র প্রয়োজনীয় কমান্ডের অনুমতি রয়েছে';
}
