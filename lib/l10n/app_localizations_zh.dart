// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appTitle => 'DARS 语言';

  @override
  String get cameraView => '摄像头视图';

  @override
  String get settings => '设置';

  @override
  String get ruleEditor => '规则编辑器';

  @override
  String get calibration => '校准';

  @override
  String get profiles => '配置文件';

  @override
  String get global => '全局';

  @override
  String get media => '媒体';

  @override
  String get presentation => '演示';

  @override
  String get driving => '驾驶';

  @override
  String gestureDetected(String gesture) {
    return '检测到手势：$gesture';
  }

  @override
  String confidence(int confidence) {
    return '置信度：$confidence%';
  }

  @override
  String get startCalibration => '开始校准';

  @override
  String get calibrationComplete => '校准完成';

  @override
  String get enableRule => '启用规则';

  @override
  String get disableRule => '禁用规则';

  @override
  String get addRule => '添加规则';

  @override
  String get editRule => '编辑规则';

  @override
  String get deleteRule => '删除规则';

  @override
  String get ruleName => '规则名称';

  @override
  String get trigger => '触发器';

  @override
  String get action => '动作';

  @override
  String get cooldown => '冷却时间（毫秒）';

  @override
  String get minConfidence => '最小置信度';

  @override
  String get save => '保存';

  @override
  String get cancel => '取消';

  @override
  String get confirm => '确认';

  @override
  String get back => '返回';

  @override
  String get cameraPermissionRequired => '使用此应用需要摄像头权限';

  @override
  String get grantPermission => '授予权限';

  @override
  String get faceNotDetected => '未检测到面部';

  @override
  String get faceDetected => '检测到面部';

  @override
  String get gestureHistory => '手势历史';

  @override
  String get exportRules => '导出规则';

  @override
  String get importRules => '导入规则';

  @override
  String get language => '语言';

  @override
  String get sensitivity => '灵敏度';

  @override
  String get safeMode => '安全模式';

  @override
  String get safeModeDescription => '仅允许基本命令';
}
