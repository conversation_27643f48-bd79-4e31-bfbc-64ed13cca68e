import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:camera/camera.dart';
import 'package:permission_handler/permission_handler.dart';
import '../providers/app_providers.dart';
import '../widgets/gesture_hud.dart';
import '../widgets/gesture_history_widget.dart';
import '../widgets/confidence_indicator.dart';
import '../services/action_executor_service.dart';
import '../services/notification_service.dart';
import 'settings_screen.dart';
// TODO: Add localization support later

class CameraScreen extends ConsumerStatefulWidget {
  const CameraScreen({super.key});

  @override
  ConsumerState<CameraScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends ConsumerState<CameraScreen> {
  CameraController? _cameraController;
  List<CameraDescription>? _cameras;
  bool _isCameraInitialized = false;
  bool _hasPermission = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  @override
  void dispose() {
    _cameraController?.stopImageStream();
    _cameraController?.dispose();
    super.dispose();
  }

  Future<void> _initializeCamera() async {
    try {
      // Request camera permission
      final status = await Permission.camera.request();
      if (status != PermissionStatus.granted) {
        setState(() {
          _hasPermission = false;
          _errorMessage = 'Camera permission is required';
        });
        return;
      }

      setState(() {
        _hasPermission = true;
      });

      // Get available cameras
      _cameras = await availableCameras();
      if (_cameras == null || _cameras!.isEmpty) {
        setState(() {
          _errorMessage = 'No cameras available';
        });
        return;
      }

      // Initialize front camera (preferred for face detection)
      final frontCamera = _cameras!.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.front,
        orElse: () => _cameras!.first,
      );

      _cameraController = CameraController(
        frontCamera,
        ResolutionPreset.medium,
        enableAudio: false,
      );

      await _cameraController!.initialize();

      setState(() {
        _isCameraInitialized = true;
      });

      // Start image stream for real-time processing
      await _cameraController!.startImageStream(_processCameraImage);

      // Start gesture detection
      await ref.read(gestureDetectionServiceProvider).startDetection();

    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to initialize camera: $e';
      });
    }
  }

  void _processCameraImage(CameraImage image) {
    // Process camera image for face detection and metrics
    ref.read(gestureDetectionServiceProvider).processCameraImage(image);
  }

  @override
  Widget build(BuildContext context) {
    final isDetecting = ref.watch(isDetectingProvider);
    final currentProfile = ref.watch(currentProfileProvider);
    final safeMode = ref.watch(safeModeProvider);

    // Listen to detected gestures and process them through rule engine
    ref.listen(gestureStreamProvider, (previous, next) {
      next.whenData((gesture) {
        // Add to history
        ref.read(gestureHistoryProvider.notifier).addGesture(gesture);

        // Process through rule engine
        ref.read(ruleEngineServiceProvider).processGesture(gesture);
      });
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('DARS Language'),
        backgroundColor: Colors.black87,
        foregroundColor: Colors.white,
        actions: [
          // Profile indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: safeMode ? Colors.orange : Colors.blue,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              safeMode ? 'SAFE | $currentProfile' : currentProfile.toUpperCase(),
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          // Settings button
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              );
            },
          ),
        ],
      ),
      body: _buildBody(context, isDetecting),
      floatingActionButton: _buildFloatingActionButton(isDetecting),
    );
  }

  Widget _buildBody(BuildContext context, bool isDetecting) {
    if (!_hasPermission) {
      return _buildPermissionRequest();
    }

    if (_errorMessage != null) {
      return _buildErrorView();
    }

    if (!_isCameraInitialized) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Stack(
      children: [
        // Camera preview
        Positioned.fill(
          child: CameraPreview(_cameraController!),
        ),
        
        // HUD overlay
        Positioned.fill(
          child: GestureHUD(),
        ),

        
        // Confidence indicator
        const Positioned(
          top: 16,
          left: 16,
          child: ConfidenceIndicator(),
        ),
        
        // Detection status
        Positioned(
          top: 16,
          right: 16,
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  isDetecting ? Icons.visibility : Icons.visibility_off,
                  color: isDetecting ? Colors.green : Colors.red,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  isDetecting ? 'ACTIVE' : 'INACTIVE',
                  style: TextStyle(
                    color: isDetecting ? Colors.green : Colors.red,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
        
        // Gesture history - SUPPRIMÉ
        
        // Action feedback
        Positioned(
          bottom: 16,
          left: 16,
          right: 16,
          child: _buildActionFeedback(),
        ),
      ],
    );
  }

  Widget _buildPermissionRequest() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.camera_alt,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            'Camera permission is required to use this app',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _initializeCamera,
            child: const Text('Grant Permission'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            _errorMessage!,
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _errorMessage = null;
              });
              _initializeCamera();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildActionFeedback() {
    return Consumer(
      builder: (context, ref, child) {
        final actionStream = ref.watch(actionExecutedStreamProvider);
        
        return actionStream.when(
          data: (action) => Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    action,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          loading: () => const SizedBox.shrink(),
          error: (error, _) => Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Error: $error',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFloatingActionButton(bool isDetecting) {
    return FloatingActionButton(
      onPressed: () async {
        if (isDetecting) {
          await ref.read(isDetectingProvider.notifier).stopDetection();
          await NotificationService.stopForegroundService();
        } else {
          await ref.read(isDetectingProvider.notifier).startDetection();
          await NotificationService.startForegroundService();
          await NotificationService.updateNotificationStatus("Actif - Détection en cours");
        }
      },
      backgroundColor: isDetecting ? Colors.red : Colors.green,
      child: Icon(
        isDetecting ? Icons.stop : Icons.play_arrow,
        color: Colors.white,
      ),
    );
  }

}
