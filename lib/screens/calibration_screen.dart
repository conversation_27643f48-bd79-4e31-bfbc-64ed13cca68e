import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/app_providers.dart';
import '../models/gesture.dart';
// TODO: Add localization support later

class CalibrationScreen extends ConsumerStatefulWidget {
  const CalibrationScreen({super.key});

  @override
  ConsumerState<CalibrationScreen> createState() => _CalibrationScreenState();
}

class _CalibrationScreenState extends ConsumerState<CalibrationScreen> {
  bool _isCalibrating = false;
  String _currentStep = '';
  int _stepIndex = 0;
  double _stepProgress = 0.0;
  int _detectionCount = 0;
  int _requiredDetections = 0;
  DateTime _lastDetectionTime = DateTime.now();
  int _consecutiveDetections = 0; // To avoid false detections

  // 🎯 CALIBRATION MEDIAPIPE - 5 étapes optimisées pour MediaPipe Holistic
  final List<Map<String, dynamic>> _calibrationSteps = [
    {
      'title': '1️⃣ Position du visage',
      'description': 'Regardez droit vers la caméra et restez centré. MediaPipe détecte votre visage.',
      'type': 'face_detection',
      'required': 25, // 25 détections stables avec MediaPipe
    },
    {
      'title': '2️⃣ Clignements des yeux',
      'description': 'Clignez des yeux naturellement, 5 fois de suite. MediaPipe analyse vos yeux.',
      'type': 'blink_detection',
      'required': 5, // 5 clignements détectés
    },
    {
      'title': '3️⃣ Ouverture de la bouche',
      'description': 'Ouvrez grand la bouche et maintenez. MediaPipe mesure l\'ouverture.',
      'type': 'mouth_open',
      'required': 15, // 15 détections de bouche ouverte
    },
    {
      'title': '4️⃣ Mouvement gauche-droite',
      'description': 'Tournez lentement la tête à gauche, puis à droite. MediaPipe suit les mouvements.',
      'type': 'head_yaw',
      'required': 8, // 8 détections de mouvement horizontal
    },
    {
      'title': '5️⃣ Mouvement haut-bas',
      'description': 'Hochez la tête vers le haut, puis vers le bas. MediaPipe analyse les mouvements.',
      'type': 'head_pitch',
      'required': 8, // 8 détections de mouvement vertical
    },
  ];

  @override
  Widget build(BuildContext context) {
    final calibrationData = ref.watch(calibrationDataProvider);
    final metricsStream = ref.watch(metricsStreamProvider);

    // Listen to metrics for automatic step progression
    ref.listen<AsyncValue<GestureMetrics>>(metricsStreamProvider, (previous, next) {
      if (_isCalibrating && next.hasValue) {
        _processMetricsForCalibration(next.value!);
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('Calibration'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current calibration values
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Calibration',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildCalibrationSlider(
                      'EAR Threshold',
                      calibrationData.earThreshold,
                      0.1,
                      0.5,
                      (value) => ref.read(calibrationDataProvider.notifier).updateEarThreshold(value),
                    ),
                    _buildCalibrationSlider(
                      'MAR Threshold',
                      calibrationData.marThreshold,
                      0.3,
                      1.0,
                      (value) => ref.read(calibrationDataProvider.notifier).updateMarThreshold(value),
                    ),
                    _buildCalibrationSlider(
                      'Head Yaw Threshold',
                      calibrationData.yawThreshold,
                      5.0,
                      30.0,
                      (value) => _updateYawThreshold(value),
                    ),
                    _buildCalibrationSlider(
                      'Head Pitch Threshold',
                      calibrationData.pitchThreshold,
                      5.0,
                      30.0,
                      (value) => _updatePitchThreshold(value),
                    ),
                    _buildCalibrationSlider(
                      'Head Roll Threshold',
                      calibrationData.rollThreshold,
                      5.0,
                      30.0,
                      (value) => _updateRollThreshold(value),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Real-time metrics
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Real-time Metrics',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    metricsStream.when(
                      data: (metrics) => Column(
                        children: [
                          _buildMetricRow('EAR', metrics.ear, calibrationData.earThreshold),
                          _buildMetricRow('MAR', metrics.mar, calibrationData.marThreshold),
                          _buildMetricRow('Yaw', metrics.headPose.yaw.abs(), calibrationData.yawThreshold),
                          _buildMetricRow('Pitch', metrics.headPose.pitch.abs(), calibrationData.pitchThreshold),
                          _buildMetricRow('Roll', metrics.headPose.roll.abs(), calibrationData.rollThreshold),
                        ],
                      ),
                      loading: () => const Text('Waiting for camera data...'),
                      error: (_, __) => const Text('Error reading metrics'),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Calibration process
            if (_isCalibrating) ...[
              Card(
                color: Colors.blue.withValues(alpha: 0.1),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Text(
                        'Calibration Step ${_stepIndex + 1}/${_calibrationSteps.length}',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _calibrationSteps[_stepIndex]['title'],
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _calibrationSteps[_stepIndex]['description'],
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),

                      // Step progress bar
                      Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Progress: ${_detectionCount}/${_requiredDetections}',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                              Text(
                                '${(_stepProgress * 100).toInt()}%',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: _stepProgress >= 1.0 ? Colors.green : Colors.blue,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          LinearProgressIndicator(
                            value: _stepProgress,
                            backgroundColor: Colors.grey[300],
                            valueColor: AlwaysStoppedAnimation<Color>(
                              _stepProgress >= 1.0 ? Colors.green : Colors.blue,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Overall progress
                      Column(
                        children: [
                          Text(
                            'Overall Progress',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 4),
                          LinearProgressIndicator(
                            value: (_stepIndex + _stepProgress) / _calibrationSteps.length,
                            backgroundColor: Colors.grey[200],
                            valueColor: const AlwaysStoppedAnimation<Color>(Colors.orange),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Status indicator
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: _stepProgress >= 1.0 ? Colors.green.withValues(alpha: 0.1) : Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: _stepProgress >= 1.0 ? Colors.green : Colors.blue,
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              _stepProgress >= 1.0 ? Icons.check_circle : Icons.visibility,
                              size: 16,
                              color: _stepProgress >= 1.0 ? Colors.green : Colors.blue,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              _stepProgress >= 1.0 ? 'Step Complete!' : 'Detecting...',
                              style: TextStyle(
                                color: _stepProgress >= 1.0 ? Colors.green : Colors.blue,
                                fontWeight: FontWeight.w500,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 16),

                      // 🚀 PROGRESSION AUTOMATIQUE - Plus de bouton "Next"
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (_stepProgress >= 1.0) ...[
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                              decoration: BoxDecoration(
                                color: Colors.green.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(color: Colors.green, width: 1),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(Icons.check_circle, color: Colors.green, size: 16),
                                  const SizedBox(width: 8),
                                  Text(
                                    _stepIndex == _calibrationSteps.length - 1
                                        ? 'Calibration terminée !'
                                        : 'Étape suivante...',
                                    style: TextStyle(
                                      color: Colors.green,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ] else ...[
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                              decoration: BoxDecoration(
                                color: Colors.blue.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(color: Colors.blue, width: 1),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'En cours...',
                                    style: TextStyle(
                                      color: Colors.blue,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Bouton Cancel seulement
                      Center(
                        child: TextButton(
                          onPressed: _stopCalibration,
                          child: const Text('Annuler la calibration'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ] else ...[
              Center(
                child: ElevatedButton.icon(
                  onPressed: _startCalibration,
                  icon: const Icon(Icons.tune),
                  label: const Text('Start Calibration'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  ),
                ),
              ),
            ],

            const SizedBox(height: 32),

            // Reset to defaults
            Center(
              child: TextButton(
                onPressed: _resetToDefaults,
                child: const Text('Reset to Defaults'),
              ),
            ),

            const SizedBox(height: 16), // Espace en bas pour éviter le débordement
          ],
        ),
      ),
    );
  }

  Widget _buildCalibrationSlider(
    String label,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label: ${value.toStringAsFixed(3)}',
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: 100,
          onChanged: onChanged,
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildMetricRow(String label, double value, double threshold) {
    final isAboveThreshold = value > threshold;
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 60,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value.toStringAsFixed(3),
              style: TextStyle(
                color: isAboveThreshold ? Colors.green : Colors.grey,
                fontFamily: 'monospace',
              ),
            ),
          ),
          Icon(
            isAboveThreshold ? Icons.check_circle : Icons.circle_outlined,
            color: isAboveThreshold ? Colors.green : Colors.grey,
            size: 16,
          ),
        ],
      ),
    );
  }

  void _startCalibration() {
    setState(() {
      _isCalibrating = true;
      _stepIndex = 0;
      _stepProgress = 0.0;
      _detectionCount = 0;
      _requiredDetections = _calibrationSteps[0]['required'];
      _consecutiveDetections = 0; // Reset for start
      _lastDetectionTime = DateTime.now(); // Reset timer
    });

    print('🚀 MEDIAPIPE CALIBRATION: Starting calibration');
    print('   📋 Step 1: ${_calibrationSteps[0]['title']}');
    print('   🎯 Required detections: $_requiredDetections');
  }

  void _processMetricsForCalibration(GestureMetrics metrics) {
    // DEBUG: Always log calibration status
    print('🔍 CALIBRATION DEBUG: _isCalibrating=$_isCalibrating, _stepIndex=$_stepIndex, confidence=${metrics.confidence}');

    if (!_isCalibrating) {
      print('❌ CALIBRATION DEBUG: Not calibrating, returning');
      return;
    }

    // 🚨 CRITICAL FIX: Ignore metrics when no face is detected (confidence = 0.0)
    if (metrics.confidence <= 0.0) {
      print('❌ CALIBRATION DEBUG: No face detected (confidence=${metrics.confidence}), ignoring metrics');
      return;
    }

    final currentStep = _calibrationSteps[_stepIndex];
    final stepType = currentStep['type'];
    bool detected = false;

    // DEBUG: Log current step and ALL metrics values
    print('🔍 CALIBRATION DEBUG: Processing step ${_stepIndex + 1}/5: $stepType');
    print('🔍 METRICS DEBUG: EAR=${metrics.ear.toStringAsFixed(3)}, MAR=${metrics.mar.toStringAsFixed(3)}, Confidence=${metrics.confidence.toStringAsFixed(3)}');
    print('🔍 METRICS DEBUG: HeadPose Yaw=${metrics.headPose.yaw.toStringAsFixed(1)}°, Pitch=${metrics.headPose.pitch.toStringAsFixed(1)}°, Roll=${metrics.headPose.roll.toStringAsFixed(1)}°');

    // 🎯 CALIBRATION MEDIAPIPE - Critères RÉALISTES et FONCTIONNELS
    switch (stepType) {
      case 'face_detection':
        // Step 1: Face centered and stable - REALISTIC criteria
        detected = metrics.headPose.yaw.abs() < 10 &&     // Realistic - face centered
                  metrics.headPose.pitch.abs() < 10 &&    // Realistic - face centered
                  metrics.headPose.roll.abs() < 10 &&     // Realistic - face centered
                  metrics.confidence > 0.7;               // Realistic confidence
        break;

      case 'blink_detection':
        // Step 2: Real blink detected - WORKING criteria
        final earThreshold = ref.read(calibrationDataProvider).earThreshold;
        detected = metrics.ear < (earThreshold * 0.7) &&  // WORKING - real blink
                  metrics.confidence > 0.6;               // Lower confidence for blinks
        break;

      case 'mouth_open':
        // Step 3: Mouth really open - FIXED with REALISTIC threshold
        // FIXED: Use realistic MAR threshold instead of calibration data
        final realisticMARThreshold = 0.20; // Realistic mouth open threshold
        detected = metrics.mar > realisticMARThreshold && // FIXED - realistic threshold
                  metrics.confidence > 0.6;               // Lower confidence for mouth

        // DEBUG: Log mouth detection values
        print('🔍 MOUTH DEBUG: MAR=${metrics.mar.toStringAsFixed(3)}, RealisticThreshold=${realisticMARThreshold.toStringAsFixed(3)}, Detected=$detected');
        break;

      case 'head_yaw':
        // Step 4: Head movement left-right - WORKING criteria
        detected = metrics.headPose.yaw.abs() > 15 &&     // WORKING - significant movement
                  metrics.confidence > 0.6;               // Lower confidence for movement
        break;

      case 'head_pitch':
        // Step 5: Head movement up-down - WORKING criteria
        detected = metrics.headPose.pitch.abs() > 15 &&   // WORKING - significant movement
                  metrics.confidence > 0.6;               // Lower confidence for movement
        break;
    }

    // 🕐 COOLDOWN SYSTEM - Avoid detections too fast
    final now = DateTime.now();
    final timeSinceLastDetection = now.difference(_lastDetectionTime).inMilliseconds;

    if (detected) {
      // FIXED: Require minimum delay between detections (2000ms = 2 seconds)
      if (timeSinceLastDetection > 2000) {
        _consecutiveDetections++;
        _lastDetectionTime = now;

        // SIMPLIFIED: Only require 1 consecutive detection for all steps
        int requiredConsecutive = 1;
        switch (stepType) {
          case 'face_detection':
            requiredConsecutive = 1; // FIXED - 1 detection every 2 seconds
            break;
          case 'blink_detection':
            requiredConsecutive = 1; // FIXED - 1 blink detection
            break;
          case 'mouth_open':
            requiredConsecutive = 1; // FIXED - 1 mouth open detection
            break;
          case 'head_yaw':
          case 'head_pitch':
            requiredConsecutive = 1; // FIXED - 1 movement detection
            break;
        }

        // Compter seulement si on a assez de détections consécutives
        if (_consecutiveDetections >= requiredConsecutive) {
          setState(() {
            _detectionCount++;
            _stepProgress = _detectionCount / _requiredDetections;
            _consecutiveDetections = 0; // Reset pour la prochaine détection

            // Debug: Display MediaPipe detections
            print('🎯 MEDIAPIPE CALIBRATION Step ${_stepIndex + 1}: Detection ${_detectionCount}/${_requiredDetections} (${(_stepProgress * 100).toInt()}%)');
            print('   📊 Metrics: EAR=${metrics.ear.toStringAsFixed(3)}, MAR=${metrics.mar.toStringAsFixed(3)}, Confidence=${metrics.confidence.toStringAsFixed(3)}');
            print('   🎭 Head Pose: Yaw=${metrics.headPose.yaw.toStringAsFixed(1)}°, Pitch=${metrics.headPose.pitch.toStringAsFixed(1)}°, Roll=${metrics.headPose.roll.toStringAsFixed(1)}°');
            print('   ⏱️ Time since last detection: ${timeSinceLastDetection}ms');

            // 🚀 PROGRESSION AUTOMATIQUE - Seulement à 100%
            if (_stepProgress >= 1.0) {
              print('✅ MEDIAPIPE CALIBRATION Step ${_stepIndex + 1} completed!');
              // Longer delay to give user time
              Future.delayed(const Duration(milliseconds: 3000), () {
                if (mounted && _isCalibrating) {
                  if (_stepIndex < _calibrationSteps.length - 1) {
                    _nextCalibrationStep();
                  } else {
                    _completeCalibration();
                  }
                }
              });
            }
          });
        }
      }
    } else {
      // Reset consecutive detections if no detection
      if (timeSinceLastDetection > 3000) { // 3 seconds without detection
        _consecutiveDetections = 0;
      }
    }
  }

  void _nextCalibrationStep() {
    if (_stepIndex < _calibrationSteps.length - 1) {
      setState(() {
        _stepIndex++;
        _stepProgress = 0.0;
        _detectionCount = 0;
        _requiredDetections = _calibrationSteps[_stepIndex]['required'];
        _consecutiveDetections = 0; // Reset for new step
        _lastDetectionTime = DateTime.now(); // Reset timer
      });

      print('🔄 MEDIAPIPE CALIBRATION: Moving to step ${_stepIndex + 1}/${_calibrationSteps.length}');
      print('   📋 ${_calibrationSteps[_stepIndex]['title']}');
      print('   🎯 Required detections: $_requiredDetections');
    } else {
      _completeCalibration();
    }
  }

  void _completeCalibration() {
    setState(() {
      _isCalibrating = false;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Calibration completed successfully!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _stopCalibration() {
    setState(() {
      _isCalibrating = false;
      _stepProgress = 0.0;
      _detectionCount = 0;
      _stepIndex = 0;
      _consecutiveDetections = 0; // Reset
      _lastDetectionTime = DateTime.now(); // Reset
    });

    print('⏹️ MEDIAPIPE CALIBRATION: Calibration stopped');
  }

  void _resetToDefaults() {
    ref.read(calibrationDataProvider.notifier).updateCalibration(
      CalibrationData.defaultCalibration,
    );
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Calibration reset to defaults'),
      ),
    );
  }

  void _updateYawThreshold(double value) {
    final current = ref.read(calibrationDataProvider);
    final updated = CalibrationData(
      earThreshold: current.earThreshold,
      marThreshold: current.marThreshold,
      yawThreshold: value,
      pitchThreshold: current.pitchThreshold,
      rollThreshold: current.rollThreshold,
      blinkDurationShort: current.blinkDurationShort,
      blinkDurationLong: current.blinkDurationLong,
      doubleBlinksMaxInterval: current.doubleBlinksMaxInterval,
      timestamp: DateTime.now().millisecondsSinceEpoch.toDouble(),
    );
    ref.read(calibrationDataProvider.notifier).updateCalibration(updated);
  }

  void _updatePitchThreshold(double value) {
    final current = ref.read(calibrationDataProvider);
    final updated = CalibrationData(
      earThreshold: current.earThreshold,
      marThreshold: current.marThreshold,
      yawThreshold: current.yawThreshold,
      pitchThreshold: value,
      rollThreshold: current.rollThreshold,
      blinkDurationShort: current.blinkDurationShort,
      blinkDurationLong: current.blinkDurationLong,
      doubleBlinksMaxInterval: current.doubleBlinksMaxInterval,
      timestamp: DateTime.now().millisecondsSinceEpoch.toDouble(),
    );
    ref.read(calibrationDataProvider.notifier).updateCalibration(updated);
  }

  void _updateRollThreshold(double value) {
    final current = ref.read(calibrationDataProvider);
    final updated = CalibrationData(
      earThreshold: current.earThreshold,
      marThreshold: current.marThreshold,
      yawThreshold: current.yawThreshold,
      pitchThreshold: current.pitchThreshold,
      rollThreshold: value,
      blinkDurationShort: current.blinkDurationShort,
      blinkDurationLong: current.blinkDurationLong,
      doubleBlinksMaxInterval: current.doubleBlinksMaxInterval,
      timestamp: DateTime.now().millisecondsSinceEpoch.toDouble(),
    );
    ref.read(calibrationDataProvider.notifier).updateCalibration(updated);
  }
}
