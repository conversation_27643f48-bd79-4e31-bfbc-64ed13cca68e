import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/app_providers.dart';
import 'rule_editor_screen.dart';
import 'calibration_screen.dart';
// TODO: Add localization support later

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentProfile = ref.watch(currentProfileProvider);
    final safeMode = ref.watch(safeModeProvider);
    final rulesAsync = ref.watch(rulesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Profile Selection
          Card(
            child: ListTile(
              leading: const Icon(Icons.person),
              title: const Text('Profiles'),
              subtitle: Text(currentProfile),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showProfileSelector(context, ref),
            ),
          ),

          const SizedBox(height: 8),

          // Safe Mode Toggle
          Card(
            child: SwitchListTile(
              secondary: const Icon(Icons.security),
              title: const Text('Safe Mode'),
              subtitle: const Text('Only essential commands are allowed'),
              value: safeMode,
              onChanged: (value) {
                ref.read(safeModeProvider.notifier).setSafeMode(value);
              },
            ),
          ),

          const SizedBox(height: 8),

          // Rule Editor
          Card(
            child: ListTile(
              leading: const Icon(Icons.rule),
              title: const Text('Rule Editor'),
              subtitle: rulesAsync.when(
                data: (ruleSet) => Text('${ruleSet?.rules.length ?? 0} rules'),
                loading: () => const Text('Loading...'),
                error: (_, __) => const Text('Error loading rules'),
              ),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const RuleEditorScreen()),
                );
              },
            ),
          ),

          const SizedBox(height: 8),

          // Calibration
          Card(
            child: ListTile(
              leading: const Icon(Icons.tune),
              title: const Text('Calibration'),
              subtitle: const Text('Adjust gesture sensitivity'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const CalibrationScreen()),
                );
              },
            ),
          ),

          const SizedBox(height: 8),

          // Language Selection
          Card(
            child: ListTile(
              leading: const Icon(Icons.language),
              title: const Text('Language'),
              subtitle: const Text('English'), // TODO: Get current locale
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showLanguageSelector(context),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Export/Import Rules
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _exportRules(context, ref),
                  icon: const Icon(Icons.upload),
                  label: const Text('Export Rules'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _importRules(context, ref),
                  icon: const Icon(Icons.download),
                  label: const Text('Import Rules'),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // App Information
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'DARS Language v1.0.0',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Facial gesture control application using MediaPipe for real-time gesture recognition.',
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showProfileSelector(BuildContext context, WidgetRef ref) {
    final rulesAsync = ref.read(rulesProvider);
    
    rulesAsync.whenData((ruleSet) {
      if (ruleSet == null) return;
      
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Profiles'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: ruleSet.profiles.map((profile) {
              return RadioListTile<String>(
                title: Text(profile.toUpperCase()),
                value: profile,
                groupValue: ref.read(currentProfileProvider),
                onChanged: (value) {
                  if (value != null) {
                    ref.read(currentProfileProvider.notifier).setProfile(value);
                    Navigator.pop(context);
                  }
                },
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ],
        ),
      );
    });
  }

  void _showLanguageSelector(BuildContext context) {
    final languages = [
      {'code': 'en', 'name': 'English'},
      {'code': 'fr', 'name': 'Français'},
      {'code': 'es', 'name': 'Español'},
      {'code': 'zh', 'name': '中文'},
      {'code': 'ar', 'name': 'العربية'},
      {'code': 'hi', 'name': 'हिन्दी'},
      {'code': 'bn', 'name': 'বাংলা'},
      {'code': 'pt', 'name': 'Português'},
      {'code': 'ru', 'name': 'Русский'},
      {'code': 'ja', 'name': '日本語'},
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Language'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: languages.map((lang) {
            return ListTile(
              title: Text(lang['name']!),
              onTap: () {
                // TODO: Implement language change
                Navigator.pop(context);
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _exportRules(BuildContext context, WidgetRef ref) async {
    try {
      await ref.read(ruleEngineServiceProvider).exportRules();

      // TODO: Implement file sharing/export
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Rules exported successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _importRules(BuildContext context, WidgetRef ref) async {
    // TODO: Implement file picker and import
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Import functionality coming soon'),
      ),
    );
  }
}
