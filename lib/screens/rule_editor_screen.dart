import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/app_providers.dart';
import '../models/rule.dart';
// TODO: Add localization support later

class RuleEditorScreen extends ConsumerWidget {
  const RuleEditorScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentProfile = ref.watch(currentProfileProvider);
    final currentProfileRules = ref.watch(currentProfileRulesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Rule Editor'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddRuleDialog(context, ref, currentProfile),
          ),
        ],
      ),
      body: Column(
        children: [
          // Profile indicator
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Colors.blue.withValues(alpha: 0.1),
            child: Text(
              'Profile: ${currentProfile.toUpperCase()}',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          // Rules list
          Expanded(
            child: currentProfileRules.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.rule,
                          size: 64,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No rules for this profile',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton(
                          onPressed: () => _showAddRuleDialog(context, ref, currentProfile),
                          child: const Text('Add Rule'),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: currentProfileRules.length,
                    itemBuilder: (context, index) {
                      final rule = currentProfileRules[index];
                      return _buildRuleCard(context, ref, rule);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildRuleCard(BuildContext context, WidgetRef ref, Rule rule) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ExpansionTile(
        leading: Switch(
          value: rule.enabled,
          onChanged: (value) {
            ref.read(rulesProvider.notifier).toggleRuleEnabled(rule.name);
          },
        ),
        title: Text(
          rule.name,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: rule.enabled ? Colors.black : Colors.grey,
          ),
        ),
        subtitle: Text(_getTriggerDescription(rule.trigger)),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInfoRow('Trigger', _getTriggerDescription(rule.trigger)),
                _buildInfoRow('Action', '${rule.action.type}: ${rule.action.name}'),
                _buildInfoRow('Cooldown', '${rule.cooldownMs}ms'),
                _buildInfoRow('Min Confidence', '${(rule.conditions.minConfidence * 100).round()}%'),
                
                const SizedBox(height: 16),
                
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => _showEditRuleDialog(context, ref, rule),
                      child: const Text('Edit Rule'),
                    ),
                    const SizedBox(width: 8),
                    TextButton(
                      onPressed: () => _showDeleteConfirmation(context, ref, rule),
                      style: TextButton.styleFrom(foregroundColor: Colors.red),
                      child: const Text('Delete Rule'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  String _getTriggerDescription(Trigger trigger) {
    switch (trigger.type) {
      case 'symbol':
        return 'Symbol: ${trigger.symbol}';
      case 'modifier_symbol':
        return 'Modifier: ${trigger.modifier} + ${trigger.symbol}';
      case 'sequence':
        return 'Sequence: ${trigger.sequence?.join(' → ') ?? ''}';
      default:
        return trigger.type;
    }
  }

  void _showAddRuleDialog(BuildContext context, WidgetRef ref, String profile) {
    // TODO: Implement add rule dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Rule'),
        content: const Text('Add rule functionality coming soon'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showEditRuleDialog(BuildContext context, WidgetRef ref, Rule rule) {
    // TODO: Implement edit rule dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Rule'),
        content: const Text('Edit rule functionality coming soon'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, WidgetRef ref, Rule rule) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Rule'),
        content: Text('Are you sure you want to delete "${rule.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              ref.read(rulesProvider.notifier).deleteRule(rule.name);
              Navigator.pop(context);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
