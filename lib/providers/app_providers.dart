import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/gesture.dart';
import '../models/rule.dart';
import '../services/gesture_detection_service.dart';
import '../services/rule_engine_service.dart';

// Service providers
final gestureDetectionServiceProvider = Provider<GestureDetectionService>((ref) {
  final service = GestureDetectionService();
  ref.onDispose(() => service.dispose());
  return service;
});

final ruleEngineServiceProvider = Provider<RuleEngineService>((ref) {
  final service = RuleEngineService();
  ref.onDispose(() => service.dispose());
  return service;
});

// State providers
final currentProfileProvider = StateNotifierProvider<CurrentProfileNotifier, String>((ref) {
  return CurrentProfileNotifier(ref.read(ruleEngineServiceProvider));
});

final safeModeProvider = StateNotifierProvider<SafeModeNotifier, bool>((ref) {
  return SafeModeNotifier(ref.read(ruleEngineServiceProvider));
});

final calibrationDataProvider = StateNotifierProvider<CalibrationDataNotifier, CalibrationData>((ref) {
  return CalibrationDataNotifier(ref.read(gestureDetectionServiceProvider));
});

final isDetectingProvider = StateNotifierProvider<DetectionStateNotifier, bool>((ref) {
  return DetectionStateNotifier(ref.read(gestureDetectionServiceProvider));
});

// Stream providers
final gestureStreamProvider = StreamProvider<DetectedGesture>((ref) {
  final service = ref.read(gestureDetectionServiceProvider);
  return service.gestureStream;
});

final metricsStreamProvider = StreamProvider<GestureMetrics>((ref) {
  final service = ref.read(gestureDetectionServiceProvider);
  return service.metricsStream;
});

final faceDetectedStreamProvider = StreamProvider<bool>((ref) {
  final service = ref.read(gestureDetectionServiceProvider);
  return service.faceDetectedStream;
});

final actionExecutedStreamProvider = StreamProvider<String>((ref) {
  final service = ref.read(ruleEngineServiceProvider);
  return service.actionExecutedStream;
});

// Rules provider
final rulesProvider = StateNotifierProvider<RulesNotifier, AsyncValue<RuleSet?>>((ref) {
  return RulesNotifier(ref.read(ruleEngineServiceProvider));
});

final currentProfileRulesProvider = Provider<List<Rule>>((ref) {
  final rulesAsync = ref.watch(rulesProvider);
  final currentProfile = ref.watch(currentProfileProvider);
  
  return rulesAsync.when(
    data: (ruleSet) => ruleSet?.rules.where((rule) => rule.profile == currentProfile).toList() ?? [],
    loading: () => [],
    error: (_, __) => [],
  );
});

// Gesture history provider
final gestureHistoryProvider = StateNotifierProvider<GestureHistoryNotifier, List<DetectedGesture>>((ref) {
  return GestureHistoryNotifier();
});

// State Notifiers
class CurrentProfileNotifier extends StateNotifier<String> {
  final RuleEngineService _ruleEngineService;
  
  CurrentProfileNotifier(this._ruleEngineService) : super('global') {
    _loadCurrentProfile();
  }
  
  Future<void> _loadCurrentProfile() async {
    await _ruleEngineService.initialize();
    state = _ruleEngineService.currentProfile;
  }
  
  Future<void> setProfile(String profile) async {
    await _ruleEngineService.setCurrentProfile(profile);
    state = profile;
  }
}

class SafeModeNotifier extends StateNotifier<bool> {
  final RuleEngineService _ruleEngineService;
  
  SafeModeNotifier(this._ruleEngineService) : super(false) {
    state = _ruleEngineService.safeMode;
  }
  
  Future<void> setSafeMode(bool enabled) async {
    await _ruleEngineService.setSafeMode(enabled);
    state = enabled;
  }
}

class CalibrationDataNotifier extends StateNotifier<CalibrationData> {
  final GestureDetectionService _gestureDetectionService;
  
  CalibrationDataNotifier(this._gestureDetectionService) : super(CalibrationData.defaultCalibration);
  
  Future<void> updateCalibration(CalibrationData calibration) async {
    await _gestureDetectionService.updateCalibration(calibration);
    state = calibration;
  }
  
  void updateEarThreshold(double threshold) {
    final updated = CalibrationData(
      earThreshold: threshold,
      marThreshold: state.marThreshold,
      yawThreshold: state.yawThreshold,
      pitchThreshold: state.pitchThreshold,
      rollThreshold: state.rollThreshold,
      blinkDurationShort: state.blinkDurationShort,
      blinkDurationLong: state.blinkDurationLong,
      doubleBlinksMaxInterval: state.doubleBlinksMaxInterval,
      timestamp: DateTime.now().millisecondsSinceEpoch.toDouble(),
    );
    updateCalibration(updated);
  }
  
  void updateMarThreshold(double threshold) {
    final updated = CalibrationData(
      earThreshold: state.earThreshold,
      marThreshold: threshold,
      yawThreshold: state.yawThreshold,
      pitchThreshold: state.pitchThreshold,
      rollThreshold: state.rollThreshold,
      blinkDurationShort: state.blinkDurationShort,
      blinkDurationLong: state.blinkDurationLong,
      doubleBlinksMaxInterval: state.doubleBlinksMaxInterval,
      timestamp: DateTime.now().millisecondsSinceEpoch.toDouble(),
    );
    updateCalibration(updated);
  }
}

class DetectionStateNotifier extends StateNotifier<bool> {
  final GestureDetectionService _gestureDetectionService;
  
  DetectionStateNotifier(this._gestureDetectionService) : super(false);
  
  Future<void> startDetection() async {
    try {
      await _gestureDetectionService.startDetection();
      state = true;
    } catch (e) {
      state = false;
      rethrow;
    }
  }
  
  Future<void> stopDetection() async {
    try {
      await _gestureDetectionService.stopDetection();
      state = false;
    } catch (e) {
      // Even if stopping fails, set state to false
      state = false;
    }
  }
}

class RulesNotifier extends StateNotifier<AsyncValue<RuleSet?>> {
  final RuleEngineService _ruleEngineService;
  
  RulesNotifier(this._ruleEngineService) : super(const AsyncValue.loading()) {
    _loadRules();
  }
  
  Future<void> _loadRules() async {
    try {
      await _ruleEngineService.initialize();
      state = AsyncValue.data(_ruleEngineService.ruleSet);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
  
  Future<void> addRule(Rule rule) async {
    try {
      await _ruleEngineService.addRule(rule);
      state = AsyncValue.data(_ruleEngineService.ruleSet);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
  
  Future<void> updateRule(String ruleName, Rule updatedRule) async {
    try {
      await _ruleEngineService.updateRule(ruleName, updatedRule);
      state = AsyncValue.data(_ruleEngineService.ruleSet);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
  
  Future<void> deleteRule(String ruleName) async {
    try {
      await _ruleEngineService.deleteRule(ruleName);
      state = AsyncValue.data(_ruleEngineService.ruleSet);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
  
  Future<void> toggleRuleEnabled(String ruleName) async {
    try {
      await _ruleEngineService.toggleRuleEnabled(ruleName);
      state = AsyncValue.data(_ruleEngineService.ruleSet);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
  
  Future<void> importRules(String rulesJson) async {
    try {
      await _ruleEngineService.importRules(rulesJson);
      state = AsyncValue.data(_ruleEngineService.ruleSet);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}

class GestureHistoryNotifier extends StateNotifier<List<DetectedGesture>> {
  GestureHistoryNotifier() : super([]);
  
  void addGesture(DetectedGesture gesture) {
    final updatedHistory = [...state, gesture];
    
    // Keep only last 20 gestures
    if (updatedHistory.length > 20) {
      updatedHistory.removeAt(0);
    }
    
    state = updatedHistory;
  }
  
  void clearHistory() {
    state = [];
  }
}
