import 'dart:async';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/rule.dart';
import '../models/gesture.dart';
import 'action_executor_service.dart';

class RuleEngineService {
  static const String _rulesKey = 'dars_rules';
  static const String _currentProfileKey = 'current_profile';
  
  final ActionExecutorService _actionExecutor = ActionExecutorService();
  final Map<String, DateTime> _lastExecutionTimes = {};
  final List<DetectedGesture> _gestureSequence = [];
  
  RuleSet? _ruleSet;
  String _currentProfile = 'global';
  bool _safeMode = false;
  
  String get currentProfile => _currentProfile;
  bool get safeMode => _safeMode;
  RuleSet? get ruleSet => _ruleSet;
  
  final StreamController<String> _actionExecutedController = StreamController<String>.broadcast();
  Stream<String> get actionExecutedStream => _actionExecutedController.stream;
  
  Future<void> initialize() async {
    await _loadRules();
    await _loadCurrentProfile();
  }
  
  Future<void> _loadRules() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final rulesJson = prefs.getString(_rulesKey);
      
      if (rulesJson != null) {
        final Map<String, dynamic> data = json.decode(rulesJson);
        _ruleSet = RuleSet.fromJson(data);
      } else {
        // Load default rules from assets
        await _loadDefaultRules();
      }
    } catch (e) {
      // If loading fails, load default rules
      await _loadDefaultRules();
    }
  }
  
  Future<void> _loadDefaultRules() async {
    try {
      final String rulesJson = await rootBundle.loadString('assets/dars.json');
      final Map<String, dynamic> data = json.decode(rulesJson);
      _ruleSet = RuleSet.fromJson(data);
      await _saveRules();
    } catch (e) {
      // Create minimal default ruleset if asset loading fails
      _ruleSet = const RuleSet(
        version: '1.0',
        localeDefault: 'en',
        profiles: ['global'],
        rules: [],
      );
    }
  }
  
  Future<void> _loadCurrentProfile() async {
    final prefs = await SharedPreferences.getInstance();
    _currentProfile = prefs.getString(_currentProfileKey) ?? 'global';
  }
  
  Future<void> _saveRules() async {
    if (_ruleSet == null) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final rulesJson = json.encode(_ruleSet!.toJson());
      await prefs.setString(_rulesKey, rulesJson);
    } catch (e) {
      // Handle save error
    }
  }
  
  Future<void> setCurrentProfile(String profile) async {
    if (_ruleSet?.profiles.contains(profile) == true) {
      _currentProfile = profile;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_currentProfileKey, profile);
    }
  }
  
  Future<void> setSafeMode(bool enabled) async {
    _safeMode = enabled;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('safe_mode', enabled);
  }
  
  Future<void> processGesture(DetectedGesture gesture) async {
    if (_ruleSet == null) return;
    
    // Add gesture to sequence for sequence-based rules
    _addToGestureSequence(gesture);
    
    // Find matching rules for current profile
    final activeRules = _getActiveRules();
    
    for (final rule in activeRules) {
      if (await _shouldExecuteRule(rule, gesture)) {
        await _executeRule(rule);
        break; // Execute only the first matching rule
      }
    }
  }
  
  List<Rule> _getActiveRules() {
    if (_ruleSet == null) return [];
    
    return _ruleSet!.rules
        .where((rule) => rule.enabled && rule.profile == _currentProfile)
        .toList();
  }
  
  Future<bool> _shouldExecuteRule(Rule rule, DetectedGesture gesture) async {
    // Check cooldown
    if (_isInCooldown(rule)) return false;
    
    // Check conditions
    if (!_checkConditions(rule, gesture)) return false;
    
    // Check safe mode restrictions
    if (_safeMode && !_isSafeAction(rule.action)) return false;
    
    // Check trigger match
    return _checkTriggerMatch(rule.trigger, gesture);
  }
  
  bool _isInCooldown(Rule rule) {
    final lastExecution = _lastExecutionTimes[rule.name];
    if (lastExecution == null) return false;
    
    final cooldownEnd = lastExecution.add(Duration(milliseconds: rule.cooldownMs));
    return DateTime.now().isBefore(cooldownEnd);
  }
  
  bool _checkConditions(Rule rule, DetectedGesture gesture) {
    if (!rule.conditions.ready) return false;
    if (gesture.confidence < rule.conditions.minConfidence) return false;
    return true;
  }
  
  bool _isSafeAction(Action action) {
    // Define safe actions that are allowed in safe mode
    const safeActions = {
      'media': ['play_pause', 'next_track', 'previous_track', 'mute_toggle'],
      'navigation': ['back', 'confirm'],
      'system': ['volume_up', 'volume_down'],
    };
    
    final allowedActions = safeActions[action.type];
    return allowedActions?.contains(action.name) ?? false;
  }
  
  bool _checkTriggerMatch(Trigger trigger, DetectedGesture gesture) {
    switch (trigger.type) {
      case 'symbol':
        return trigger.symbol == gesture.symbol;
        
      case 'modifier_symbol':
        return _checkModifierSymbolMatch(trigger, gesture);
        
      case 'sequence':
        return _checkSequenceMatch(trigger);
        
      default:
        return false;
    }
  }
  
  bool _checkModifierSymbolMatch(Trigger trigger, DetectedGesture gesture) {
    if (trigger.symbol != gesture.symbol) return false;
    
    // Check if modifier gesture was detected recently
    final recentGestures = _getRecentGestures(1000); // 1 second window
    return recentGestures.any((g) => g.symbol == trigger.modifier);
  }
  
  bool _checkSequenceMatch(Trigger trigger) {
    if (trigger.sequence == null || trigger.sequence!.isEmpty) return false;
    
    final windowMs = trigger.windowMs ?? 2000;
    final recentGestures = _getRecentGestures(windowMs);
    
    if (recentGestures.length < trigger.sequence!.length) return false;
    
    // Check if the last N gestures match the sequence
    final lastGestures = recentGestures
        .skip(recentGestures.length - trigger.sequence!.length)
        .map((g) => g.symbol)
        .toList();
    
    return _listsEqual(lastGestures, trigger.sequence!);
  }
  
  bool _listsEqual<T>(List<T> a, List<T> b) {
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }
  
  List<DetectedGesture> _getRecentGestures(int windowMs) {
    final cutoffTime = DateTime.now().millisecondsSinceEpoch - windowMs;
    return _gestureSequence
        .where((g) => g.timestamp >= cutoffTime)
        .toList();
  }
  
  void _addToGestureSequence(DetectedGesture gesture) {
    _gestureSequence.add(gesture);
    
    // Remove old gestures (keep last 2 seconds)
    final cutoffTime = DateTime.now().millisecondsSinceEpoch - 2000;
    _gestureSequence.removeWhere((g) => g.timestamp < cutoffTime);
    
    // Limit sequence length
    if (_gestureSequence.length > 10) {
      _gestureSequence.removeAt(0);
    }
  }
  
  Future<void> _executeRule(Rule rule) async {
    try {
      _lastExecutionTimes[rule.name] = DateTime.now();
      
      await _actionExecutor.executeAction(rule.action);
      _actionExecutedController.add('${rule.name}: ${rule.action.name}');
      
    } catch (e) {
      // Handle execution error
      _actionExecutedController.add('Error executing ${rule.name}: $e');
    }
  }
  
  // Rule management methods
  Future<void> addRule(Rule rule) async {
    if (_ruleSet == null) return;
    
    final updatedRules = List<Rule>.from(_ruleSet!.rules)..add(rule);
    _ruleSet = _ruleSet!.copyWith(rules: updatedRules);
    await _saveRules();
  }
  
  Future<void> updateRule(String ruleName, Rule updatedRule) async {
    if (_ruleSet == null) return;
    
    final ruleIndex = _ruleSet!.rules.indexWhere((r) => r.name == ruleName);
    if (ruleIndex == -1) return;
    
    final updatedRules = List<Rule>.from(_ruleSet!.rules);
    updatedRules[ruleIndex] = updatedRule;
    
    _ruleSet = _ruleSet!.copyWith(rules: updatedRules);
    await _saveRules();
  }
  
  Future<void> deleteRule(String ruleName) async {
    if (_ruleSet == null) return;
    
    final updatedRules = _ruleSet!.rules.where((r) => r.name != ruleName).toList();
    _ruleSet = _ruleSet!.copyWith(rules: updatedRules);
    await _saveRules();
  }
  
  Future<void> toggleRuleEnabled(String ruleName) async {
    if (_ruleSet == null) return;
    
    final ruleIndex = _ruleSet!.rules.indexWhere((r) => r.name == ruleName);
    if (ruleIndex == -1) return;
    
    final rule = _ruleSet!.rules[ruleIndex];
    final updatedRule = rule.copyWith(enabled: !rule.enabled);
    
    await updateRule(ruleName, updatedRule);
  }
  
  List<Rule> getRulesForProfile(String profile) {
    if (_ruleSet == null) return [];
    return _ruleSet!.rules.where((rule) => rule.profile == profile).toList();
  }
  
  Future<String> exportRules() async {
    if (_ruleSet == null) return '{}';
    return json.encode(_ruleSet!.toJson());
  }
  
  Future<void> importRules(String rulesJson) async {
    try {
      final Map<String, dynamic> data = json.decode(rulesJson);
      _ruleSet = RuleSet.fromJson(data);
      await _saveRules();
    } catch (e) {
      throw Exception('Invalid rules format: $e');
    }
  }
  
  void dispose() {
    _actionExecutedController.close();
  }
}

extension RuleSetExtension on RuleSet {
  RuleSet copyWith({
    String? version,
    String? localeDefault,
    List<String>? profiles,
    List<Rule>? rules,
  }) {
    return RuleSet(
      version: version ?? this.version,
      localeDefault: localeDefault ?? this.localeDefault,
      profiles: profiles ?? this.profiles,
      rules: rules ?? this.rules,
    );
  }
}
