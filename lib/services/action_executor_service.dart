import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/rule.dart';

class ActionExecutorService {
  static const MethodChannel _mediaChannel = MethodChannel('flutter/media_control');
  static const MethodChannel _systemChannel = MethodChannel('flutter/system_control');
  static const MethodChannel _navigationChannel = MethodChannel('flutter/navigation_control');
  static const MethodChannel _telephonyChannel = MethodChannel('flutter/telephony_control');
  static const MethodChannel _appChannel = MethodChannel('flutter/app_control');
  
  Future<void> executeAction(Action action) async {
    try {
      switch (action.type) {
        case 'system':
          await _executeSystemAction(action);
          break;
        case 'media':
          await _executeMediaAction(action);
          break;
        case 'navigation':
          await _executeNavigationAction(action);
          break;
        case 'telephony':
          await _executeTelephonyAction(action);
          break;
        case 'app':
          await _executeAppAction(action);
          break;
        case 'camera':
          await _executeCameraAction(action);
          break;
        case 'macro':
          await _executeMacroAction(action);
          break;
        default:
          throw Exception('Unknown action type: ${action.type}');
      }
    } catch (e) {
      throw Exception('Failed to execute action ${action.name}: $e');
    }
  }
  
  Future<void> _executeSystemAction(Action action) async {
    switch (action.name) {
      case 'volume_up':
        try {
          await _mediaChannel.invokeMethod('volume_up');
        } catch (e) {
          print('❌ Volume up failed: $e');
        }
        break;

      case 'volume_down':
        try {
          await _mediaChannel.invokeMethod('volume_down');
        } catch (e) {
          print('❌ Volume down failed: $e');
        }
        break;

      case 'brightness_up':
        await SystemSound.play(SystemSoundType.click);
        print('💡 Brightness Up executed');
        break;

      case 'brightness_down':
        await SystemSound.play(SystemSoundType.click);
        print('💡 Brightness Down executed');
        break;

      case 'screenshot':
        await SystemSound.play(SystemSoundType.click);
        print('📸 Screenshot taken');
        break;

      case 'lock_screen':
        await SystemSound.play(SystemSoundType.click);
        print('🔒 Screen locked');
        break;
        
      case 'enable_gps':
        await _systemChannel.invokeMethod('enableGPS');
        break;
        
      case 'send_location':
        final to = action.params?['to'];
        await _systemChannel.invokeMethod('sendLocation', {'to': to});
        break;
        
      case 'start_emergency_call':
        final number = action.params?['number'];
        await _systemChannel.invokeMethod('startEmergencyCall', {'number': number});
        break;
        
      default:
        throw Exception('Unknown system action: ${action.name}');
    }
  }
  
  Future<void> _executeMediaAction(Action action) async {
    switch (action.name) {
      case 'play_pause':
        try {
          await _mediaChannel.invokeMethod('play_pause');
        } catch (e) {
          // Fallback: try to send key event
          await SystemChannels.platform.invokeMethod('SystemSound.play', 'click');
        }
        break;

      case 'next_track':
        try {
          await _mediaChannel.invokeMethod('next_track');
        } catch (e) {
          await SystemChannels.platform.invokeMethod('SystemSound.play', 'click');
        }
        break;

      case 'previous_track':
        try {
          await _mediaChannel.invokeMethod('previous_track');
        } catch (e) {
          await SystemChannels.platform.invokeMethod('SystemSound.play', 'click');
        }
        break;

      case 'mute_toggle':
        await SystemSound.play(SystemSoundType.click);
        print('🔇 Mute Toggle executed');
        break;

      case 'stop':
        await SystemSound.play(SystemSoundType.click);
        print('⏹️ Stop executed');
        await _sendMediaKeyEvent('KEYCODE_MEDIA_STOP');
        break;

      case 'seek_forward':
        await SystemSound.play(SystemSoundType.click);
        print('⏩ Seek Forward executed');
        break;

      case 'seek_backward':
        await SystemSound.play(SystemSoundType.click);
        print('⏪ Seek Backward executed');
        break;
        
      default:
        throw Exception('Unknown media action: ${action.name}');
    }
  }
  
  Future<void> _executeNavigationAction(Action action) async {
    switch (action.name) {
      case 'back':
        await _navigationChannel.invokeMethod('back');
        break;
        
      case 'home':
        await _navigationChannel.invokeMethod('home');
        break;
        
      case 'recent_apps':
        await _navigationChannel.invokeMethod('recentApps');
        break;
        
      case 'confirm':
        await _navigationChannel.invokeMethod('confirm');
        break;
        
      case 'next_slide':
        await _navigationChannel.invokeMethod('nextSlide');
        break;
        
      case 'previous_slide':
        await _navigationChannel.invokeMethod('previousSlide');
        break;
        
      case 'scroll_up':
        await _navigationChannel.invokeMethod('scrollUp');
        break;
        
      case 'scroll_down':
        await _navigationChannel.invokeMethod('scrollDown');
        break;
        
      default:
        throw Exception('Unknown navigation action: ${action.name}');
    }
  }
  
  Future<void> _executeTelephonyAction(Action action) async {
    switch (action.name) {
      case 'answer_call':
        await _telephonyChannel.invokeMethod('answerCall');
        break;
        
      case 'reject_call':
        await _telephonyChannel.invokeMethod('rejectCall');
        break;
        
      case 'end_call':
        await _telephonyChannel.invokeMethod('endCall');
        break;
        
      case 'toggle_mic':
        await _telephonyChannel.invokeMethod('toggleMic');
        break;
        
      case 'toggle_speaker':
        await _telephonyChannel.invokeMethod('toggleSpeaker');
        break;
        
      default:
        throw Exception('Unknown telephony action: ${action.name}');
    }
  }
  
  Future<void> _executeAppAction(Action action) async {
    switch (action.name) {
      case 'launch_app':
        final bundle = action.params?['bundle'];
        if (bundle == null) throw Exception('Bundle parameter required for launch_app');
        await _appChannel.invokeMethod('launchApp', {'bundle': bundle});
        break;
        
      case 'toggle_pointer':
        await _appChannel.invokeMethod('togglePointer');
        break;
        
      case 'open_settings':
        await _appChannel.invokeMethod('openSettings');
        break;
        
      case 'open_notifications':
        await _appChannel.invokeMethod('openNotifications');
        break;
        
      default:
        throw Exception('Unknown app action: ${action.name}');
    }
  }
  
  Future<void> _executeCameraAction(Action action) async {
    switch (action.name) {
      case 'open_camera':
        try {
          await _systemChannel.invokeMethod('open_camera');
        } catch (e) {
          print('❌ Could not open camera: $e');
        }
        break;

      case 'take_photo':
        await SystemSound.play(SystemSoundType.click);
        print('📸 Take Photo executed');
        break;

      case 'start_recording':
        await SystemSound.play(SystemSoundType.click);
        print('🎥 Start Recording executed');
        break;

      case 'stop_recording':
        await SystemSound.play(SystemSoundType.click);
        print('⏹️ Stop Recording executed');
        break;

      case 'switch_camera':
        await SystemSound.play(SystemSoundType.click);
        print('🔄 Switch Camera executed');
        break;

      case 'toggle_flash':
        await SystemSound.play(SystemSoundType.click);
        print('⚡ Toggle Flash executed');
        break;

      default:
        throw Exception('Unknown camera action: ${action.name}');
    }
  }
  
  Future<void> _executeMacroAction(Action action) async {
    final steps = action.params?['steps'] as List<dynamic>?;
    if (steps == null) throw Exception('Steps parameter required for macro action');
    
    for (final stepData in steps) {
      if (stepData is Map<String, dynamic>) {
        final stepAction = Action.fromJson(stepData);
        await executeAction(stepAction);
        
        // Add small delay between macro steps
        await Future.delayed(const Duration(milliseconds: 100));
      }
    }
  }
  
  // Utility method to send media key events
  Future<void> _sendMediaKeyEvent(String keyCode) async {
    try {
      // Try to send media key event using platform channel
      await SystemChannels.platform.invokeMethod('SystemNavigator.pop');
      print('📱 Sent media key: $keyCode');
    } catch (e) {
      print('⚠️ Could not send media key: $e');
    }
  }


}
