import 'dart:async';
import 'dart:math' as math;
import 'dart:typed_data';
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/gesture.dart';
import 'mediapipe_gesture_service.dart';

/// Service principal de détection de gestes utilisant MediaPipe Holistic
class GestureDetectionService {
  
  final StreamController<DetectedGesture> _gestureController = StreamController<DetectedGesture>.broadcast();
  final StreamController<GestureMetrics> _metricsController = StreamController<GestureMetrics>.broadcast();
  final StreamController<bool> _faceDetectedController = StreamController<bool>.broadcast();
  
  Stream<DetectedGesture> get gestureStream => _gestureController.stream;
  Stream<GestureMetrics> get metricsStream => _metricsController.stream;
  Stream<bool> get faceDetectedStream => _faceDetectedController.stream;

  bool get isInitialized => _isInitialized;
  bool get isDetecting => _isDetecting;
  
  CalibrationData _calibrationData = CalibrationData.defaultCalibration;
  bool _isInitialized = false;
  bool _isDetecting = false;
  bool _isProcessing = false;
  double _lastProcessTime = 0.0;

  // MEDIAPIPE HOLISTIC SERVICE - 543 LANDMARKS !
  late MediaPipeGestureService _mediaPipeService;
  bool _useMediaPipe = true; // MEDIAPIPE ACTIVÉ !
  
  // Gesture detection state
  int _blinkCount = 0;
  
  final List<DetectedGesture> _gestureHistory = [];
  static const int maxHistoryLength = 10;
  static const double sequenceWindowMs = 2000.0;
  
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      print('🚀 MEDIAPIPE HOLISTIC: Initialisation du service...');
      
      // Initialiser MediaPipe Holistic Service
      _mediaPipeService = MediaPipeGestureService();
      await _mediaPipeService.initialize();
      
      // Écouter les streams MediaPipe
      _mediaPipeService.gestureStream.listen((gesture) {
        _gestureController.add(gesture);
      });
      
      _mediaPipeService.faceDetectedStream.listen((detected) {
        _faceDetectedController.add(detected);
      });
      
      _mediaPipeService.metricsStream.listen((metrics) {
        _metricsController.add(metrics);
      });
      
      print('✅ MEDIAPIPE HOLISTIC initialisé avec 543 landmarks !');
      
      _isInitialized = true;
      print('✅ Service MEDIAPIPE prêt à détecter les visages !');
      
    } catch (e) {
      print('❌ Erreur d\'initialisation MediaPipe: $e');
      throw Exception('Échec de l\'initialisation MediaPipe: $e');
    }
  }
  
  Future<void> startDetection() async {
    if (!_isInitialized) await initialize();
    
    if (_isDetecting) return;

    try {
      _isDetecting = true;
      print('🎯 MEDIAPIPE: Démarrage de la détection...');
      
      // Démarrer MediaPipe Holistic
      await _mediaPipeService.startDetection();
      print('✅ MEDIAPIPE HOLISTIC en cours d\'exécution !');
      
    } catch (e) {
      throw Exception('Failed to start MediaPipe detection: $e');
    }
  }

  Future<void> stopDetection() async {
    if (!_isDetecting) return;

    try {
      _isDetecting = false;
      
      // Arrêter MediaPipe
      await _mediaPipeService.stopDetection();
      
      print('🛑 MEDIAPIPE: Détection arrêtée');
    } catch (e) {
      throw Exception('Failed to stop MediaPipe detection: $e');
    }
  }
  
  Future<void> processImage(CameraImage image) async {
    if (!_isInitialized || !_isDetecting) return;

    // Throttle processing to avoid overwhelming the system
    final now = DateTime.now().millisecondsSinceEpoch.toDouble();
    if (now - _lastProcessTime < 100) return; // Process max 10 FPS

    _lastProcessTime = now;
    _processImageWithMediaPipe(image);
  }

  // Alias pour compatibilité
  Future<void> processCameraImage(CameraImage image) async {
    return processImage(image);
  }

  Future<void> _processImageWithMediaPipe(CameraImage image) async {
    if (_isProcessing) return;
    _isProcessing = true;

    try {
      print('🎯 MEDIAPIPE: Traitement image ${image.width}x${image.height}');
      
      // Traiter avec MediaPipe Holistic (543 landmarks !)
      await _mediaPipeService.processImage(image);
      
      print('✅ MEDIAPIPE: Image traitée avec succès !');

    } catch (e) {
      print('❌ Erreur de traitement MediaPipe: $e');
    } finally {
      _isProcessing = false;
    }
  }

  void setCalibrationData(CalibrationData data) {
    _calibrationData = data;
    print('📊 Calibration data updated');
  }

  CalibrationData getCalibrationData() {
    return _calibrationData;
  }

  // Méthode pour compatibilité
  Future<void> updateCalibration(CalibrationData data) async {
    setCalibrationData(data);
  }

  void dispose() {
    _gestureController.close();
    _metricsController.close();
    _faceDetectedController.close();
    
    // Fermer MediaPipe Holistic Service
    _mediaPipeService.dispose();
    
    stopDetection();
    print('🗑️ Service de détection fermé');
  }
}
