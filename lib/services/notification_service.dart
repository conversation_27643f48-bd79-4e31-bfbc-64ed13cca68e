import 'package:flutter/services.dart';

class NotificationService {
  static const MethodChannel _channel = MethodChannel('flutter/notification_service');
  
  static bool _isServiceRunning = false;
  
  static bool get isServiceRunning => _isServiceRunning;
  
  /// Démarre le service de notification persistante
  static Future<void> startForegroundService() async {
    try {
      await _channel.invokeMethod('startForegroundService');
      _isServiceRunning = true;
      print('✅ Service de notification démarré');
    } catch (e) {
      print('❌ Erreur démarrage service: $e');
    }
  }
  
  /// Arrête le service de notification persistante
  static Future<void> stopForegroundService() async {
    try {
      await _channel.invokeMethod('stopForegroundService');
      _isServiceRunning = false;
      print('✅ Service de notification arrêté');
    } catch (e) {
      print('❌ Erreur arrêt service: $e');
    }
  }
  
  /// Met à jour le statut de la notification
  static Future<void> updateNotificationStatus(String status) async {
    try {
      await _channel.invokeMethod('updateNotificationStatus', {'status': status});
      print('✅ Statut notification mis à jour: $status');
    } catch (e) {
      print('❌ Erreur mise à jour statut: $e');
    }
  }
}
