// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rule.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Rule _$RuleFrom<PERSON>son(Map<String, dynamic> json) => Rule(
  name: json['name'] as String,
  profile: json['profile'] as String,
  trigger: Trigger.fromJson(json['trigger'] as Map<String, dynamic>),
  conditions: Conditions.fromJson(json['conditions'] as Map<String, dynamic>),
  action: Action.fromJson(json['action'] as Map<String, dynamic>),
  cooldownMs: (json['cooldown_ms'] as num).toInt(),
  enabled: json['enabled'] as bool,
);

Map<String, dynamic> _$RuleTo<PERSON>son(Rule instance) => <String, dynamic>{
  'name': instance.name,
  'profile': instance.profile,
  'trigger': instance.trigger,
  'conditions': instance.conditions,
  'action': instance.action,
  'cooldown_ms': instance.cooldownMs,
  'enabled': instance.enabled,
};

Trigger _$TriggerFromJson(Map<String, dynamic> json) => Trigger(
  type: json['type'] as String,
  symbol: json['symbol'] as String?,
  modifier: json['modifier'] as String?,
  sequence: (json['sequence'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  windowMs: (json['window_ms'] as num?)?.toInt(),
);

Map<String, dynamic> _$TriggerToJson(Trigger instance) => <String, dynamic>{
  'type': instance.type,
  'symbol': instance.symbol,
  'modifier': instance.modifier,
  'sequence': instance.sequence,
  'window_ms': instance.windowMs,
};

Conditions _$ConditionsFromJson(Map<String, dynamic> json) => Conditions(
  ready: json['ready'] as bool,
  minConfidence: (json['min_confidence'] as num).toDouble(),
);

Map<String, dynamic> _$ConditionsToJson(Conditions instance) =>
    <String, dynamic>{
      'ready': instance.ready,
      'min_confidence': instance.minConfidence,
    };

Action _$ActionFromJson(Map<String, dynamic> json) => Action(
  type: json['type'] as String,
  name: json['name'] as String,
  params: json['params'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$ActionToJson(Action instance) => <String, dynamic>{
  'type': instance.type,
  'name': instance.name,
  'params': instance.params,
};

RuleSet _$RuleSetFromJson(Map<String, dynamic> json) => RuleSet(
  version: json['version'] as String,
  localeDefault: json['locale_default'] as String,
  profiles: (json['profiles'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  rules: (json['rules'] as List<dynamic>)
      .map((e) => Rule.fromJson(e as Map<String, dynamic>))
      .toList(),
);

Map<String, dynamic> _$RuleSetToJson(RuleSet instance) => <String, dynamic>{
  'version': instance.version,
  'locale_default': instance.localeDefault,
  'profiles': instance.profiles,
  'rules': instance.rules,
};
