// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gesture.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FaceLandmarks _$FaceLandmarksFromJson(Map<String, dynamic> json) =>
    FaceLandmarks(
      landmarks: (json['landmarks'] as List<dynamic>)
          .map((e) => Point.fromJson(e as Map<String, dynamic>))
          .toList(),
      leftEye: (json['leftEye'] as List<dynamic>)
          .map((e) => Point.fromJson(e as Map<String, dynamic>))
          .toList(),
      rightEye: (json['rightEye'] as List<dynamic>)
          .map((e) => Point.fromJson(e as Map<String, dynamic>))
          .toList(),
      mouth: (json['mouth'] as List<dynamic>)
          .map((e) => Point.fromJson(e as Map<String, dynamic>))
          .toList(),
      leftIris: (json['leftIris'] as List<dynamic>)
          .map((e) => Point.fromJson(e as Map<String, dynamic>))
          .toList(),
      rightIris: (json['rightIris'] as List<dynamic>)
          .map((e) => Point.fromJson(e as Map<String, dynamic>))
          .toList(),
      timestamp: (json['timestamp'] as num).toDouble(),
    );

Map<String, dynamic> _$FaceLandmarksToJson(FaceLandmarks instance) =>
    <String, dynamic>{
      'landmarks': instance.landmarks,
      'leftEye': instance.leftEye,
      'rightEye': instance.rightEye,
      'mouth': instance.mouth,
      'leftIris': instance.leftIris,
      'rightIris': instance.rightIris,
      'timestamp': instance.timestamp,
    };

Point _$PointFromJson(Map<String, dynamic> json) => Point(
  x: (json['x'] as num).toDouble(),
  y: (json['y'] as num).toDouble(),
  z: (json['z'] as num?)?.toDouble(),
);

Map<String, dynamic> _$PointToJson(Point instance) => <String, dynamic>{
  'x': instance.x,
  'y': instance.y,
  'z': instance.z,
};

HeadPose _$HeadPoseFromJson(Map<String, dynamic> json) => HeadPose(
  yaw: (json['yaw'] as num).toDouble(),
  pitch: (json['pitch'] as num).toDouble(),
  roll: (json['roll'] as num).toDouble(),
  timestamp: (json['timestamp'] as num).toDouble(),
);

Map<String, dynamic> _$HeadPoseToJson(HeadPose instance) => <String, dynamic>{
  'yaw': instance.yaw,
  'pitch': instance.pitch,
  'roll': instance.roll,
  'timestamp': instance.timestamp,
};

GestureMetrics _$GestureMetricsFromJson(Map<String, dynamic> json) =>
    GestureMetrics(
      ear: (json['ear'] as num).toDouble(),
      mar: (json['mar'] as num).toDouble(),
      headPose: HeadPose.fromJson(json['headPose'] as Map<String, dynamic>),
      confidence: (json['confidence'] as num).toDouble(),
      timestamp: (json['timestamp'] as num).toDouble(),
    );

Map<String, dynamic> _$GestureMetricsToJson(GestureMetrics instance) =>
    <String, dynamic>{
      'ear': instance.ear,
      'mar': instance.mar,
      'headPose': instance.headPose,
      'confidence': instance.confidence,
      'timestamp': instance.timestamp,
    };

DetectedGesture _$DetectedGestureFromJson(Map<String, dynamic> json) =>
    DetectedGesture(
      type: $enumDecode(_$GestureTypeEnumMap, json['type']),
      symbol: json['symbol'] as String,
      confidence: (json['confidence'] as num).toDouble(),
      timestamp: (json['timestamp'] as num).toDouble(),
      metrics: GestureMetrics.fromJson(json['metrics'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$DetectedGestureToJson(DetectedGesture instance) =>
    <String, dynamic>{
      'type': _$GestureTypeEnumMap[instance.type]!,
      'symbol': instance.symbol,
      'confidence': instance.confidence,
      'timestamp': instance.timestamp,
      'metrics': instance.metrics,
    };

const _$GestureTypeEnumMap = {
  GestureType.blink: 'blink',
  GestureType.doubleBlink: 'doubleBlink',
  GestureType.longBlink: 'longBlink',
  GestureType.mouthOpen: 'mouthOpen',
  GestureType.smile: 'smile',
  GestureType.lipsPucker: 'lipsPucker',
  GestureType.headTiltRight: 'headTiltRight',
  GestureType.headTiltLeft: 'headTiltLeft',
  GestureType.headNodYes: 'headNodYes',
  GestureType.headShakeNo: 'headShakeNo',
};

CalibrationData _$CalibrationDataFromJson(Map<String, dynamic> json) =>
    CalibrationData(
      earThreshold: (json['earThreshold'] as num).toDouble(),
      marThreshold: (json['marThreshold'] as num).toDouble(),
      yawThreshold: (json['yawThreshold'] as num).toDouble(),
      pitchThreshold: (json['pitchThreshold'] as num).toDouble(),
      rollThreshold: (json['rollThreshold'] as num).toDouble(),
      blinkDurationShort: (json['blinkDurationShort'] as num).toDouble(),
      blinkDurationLong: (json['blinkDurationLong'] as num).toDouble(),
      doubleBlinksMaxInterval: (json['doubleBlinksMaxInterval'] as num)
          .toDouble(),
      timestamp: (json['timestamp'] as num).toDouble(),
    );

Map<String, dynamic> _$CalibrationDataToJson(CalibrationData instance) =>
    <String, dynamic>{
      'earThreshold': instance.earThreshold,
      'marThreshold': instance.marThreshold,
      'yawThreshold': instance.yawThreshold,
      'pitchThreshold': instance.pitchThreshold,
      'rollThreshold': instance.rollThreshold,
      'blinkDurationShort': instance.blinkDurationShort,
      'blinkDurationLong': instance.blinkDurationLong,
      'doubleBlinksMaxInterval': instance.doubleBlinksMaxInterval,
      'timestamp': instance.timestamp,
    };
