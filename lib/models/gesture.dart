import 'package:json_annotation/json_annotation.dart';

part 'gesture.g.dart';

enum GestureType {
  blink,
  doubleBlink,
  longBlink,
  mouthOpen,
  smile,
  lipsPucker,
  headTiltRight,
  headTiltLeft,
  headNodYes,
  headShakeNo,
}

@JsonSerializable()
class FaceLandmarks {
  final List<Point> landmarks;
  final List<Point> leftEye;
  final List<Point> rightEye;
  final List<Point> mouth;
  final List<Point> leftIris;
  final List<Point> rightIris;
  final double timestamp;

  const FaceLandmarks({
    required this.landmarks,
    required this.leftEye,
    required this.rightEye,
    required this.mouth,
    required this.leftIris,
    required this.rightIris,
    required this.timestamp,
  });

  factory FaceLandmarks.fromJson(Map<String, dynamic> json) => _$FaceLandmarksFromJson(json);
  Map<String, dynamic> toJson() => _$FaceLandmarksToJson(this);
}

@JsonSerializable()
class Point {
  final double x;
  final double y;
  final double? z;

  const Point({
    required this.x,
    required this.y,
    this.z,
  });

  factory Point.fromJson(Map<String, dynamic> json) => _$PointFromJson(json);
  Map<String, dynamic> toJson() => _$PointToJson(this);
}

@JsonSerializable()
class HeadPose {
  final double yaw;
  final double pitch;
  final double roll;
  final double timestamp;

  const HeadPose({
    required this.yaw,
    required this.pitch,
    required this.roll,
    required this.timestamp,
  });

  factory HeadPose.fromJson(Map<String, dynamic> json) => _$HeadPoseFromJson(json);
  Map<String, dynamic> toJson() => _$HeadPoseToJson(this);
}

@JsonSerializable()
class GestureMetrics {
  final double ear; // Eye Aspect Ratio
  final double mar; // Mouth Aspect Ratio
  final HeadPose headPose;
  final double confidence;
  final double timestamp;

  const GestureMetrics({
    required this.ear,
    required this.mar,
    required this.headPose,
    required this.confidence,
    required this.timestamp,
  });

  factory GestureMetrics.fromJson(Map<String, dynamic> json) => _$GestureMetricsFromJson(json);
  Map<String, dynamic> toJson() => _$GestureMetricsToJson(this);
}

@JsonSerializable()
class DetectedGesture {
  final GestureType type;
  final String symbol;
  final double confidence;
  final double timestamp;
  final GestureMetrics metrics;

  const DetectedGesture({
    required this.type,
    required this.symbol,
    required this.confidence,
    required this.timestamp,
    required this.metrics,
  });

  factory DetectedGesture.fromJson(Map<String, dynamic> json) => _$DetectedGestureFromJson(json);
  Map<String, dynamic> toJson() => _$DetectedGestureToJson(this);
}

@JsonSerializable()
class CalibrationData {
  final double earThreshold;
  final double marThreshold;
  final double yawThreshold;
  final double pitchThreshold;
  final double rollThreshold;
  final double blinkDurationShort;
  final double blinkDurationLong;
  final double doubleBlinksMaxInterval;
  final double timestamp;

  const CalibrationData({
    required this.earThreshold,
    required this.marThreshold,
    required this.yawThreshold,
    required this.pitchThreshold,
    required this.rollThreshold,
    required this.blinkDurationShort,
    required this.blinkDurationLong,
    required this.doubleBlinksMaxInterval,
    required this.timestamp,
  });

  factory CalibrationData.fromJson(Map<String, dynamic> json) => _$CalibrationDataFromJson(json);
  Map<String, dynamic> toJson() => _$CalibrationDataToJson(this);

  static CalibrationData get defaultCalibration => const CalibrationData(
    earThreshold: 0.25,
    marThreshold: 0.6,
    yawThreshold: 15.0,
    pitchThreshold: 15.0,
    rollThreshold: 15.0,
    blinkDurationShort: 0.15,
    blinkDurationLong: 0.8,
    doubleBlinksMaxInterval: 0.5,
    timestamp: 0.0,
  );
}
