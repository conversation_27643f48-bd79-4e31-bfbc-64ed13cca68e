import 'package:json_annotation/json_annotation.dart';

part 'rule.g.dart';

@JsonSerializable()
class Rule {
  final String name;
  final String profile;
  final Trigger trigger;
  final Conditions conditions;
  final Action action;
  @JsonKey(name: 'cooldown_ms')
  final int cooldownMs;
  final bool enabled;

  const Rule({
    required this.name,
    required this.profile,
    required this.trigger,
    required this.conditions,
    required this.action,
    required this.cooldownMs,
    required this.enabled,
  });

  factory Rule.fromJson(Map<String, dynamic> json) => _$RuleFromJson(json);
  Map<String, dynamic> toJson() => _$RuleToJson(this);

  Rule copyWith({
    String? name,
    String? profile,
    Trigger? trigger,
    Conditions? conditions,
    Action? action,
    int? cooldownMs,
    bool? enabled,
  }) {
    return Rule(
      name: name ?? this.name,
      profile: profile ?? this.profile,
      trigger: trigger ?? this.trigger,
      conditions: conditions ?? this.conditions,
      action: action ?? this.action,
      cooldownMs: cooldownMs ?? this.cooldownMs,
      enabled: enabled ?? this.enabled,
    );
  }
}

@JsonSerializable()
class Trigger {
  final String type;
  final String? symbol;
  final String? modifier;
  final List<String>? sequence;
  @J<PERSON><PERSON><PERSON>(name: 'window_ms')
  final int? windowMs;

  const Trigger({
    required this.type,
    this.symbol,
    this.modifier,
    this.sequence,
    this.windowMs,
  });

  factory Trigger.fromJson(Map<String, dynamic> json) => _$TriggerFromJson(json);
  Map<String, dynamic> toJson() => _$TriggerToJson(this);
}

@JsonSerializable()
class Conditions {
  final bool ready;
  @JsonKey(name: 'min_confidence')
  final double minConfidence;

  const Conditions({
    required this.ready,
    required this.minConfidence,
  });

  factory Conditions.fromJson(Map<String, dynamic> json) => _$ConditionsFromJson(json);
  Map<String, dynamic> toJson() => _$ConditionsToJson(this);
}

@JsonSerializable()
class Action {
  final String type;
  final String name;
  final Map<String, dynamic>? params;

  const Action({
    required this.type,
    required this.name,
    this.params,
  });

  factory Action.fromJson(Map<String, dynamic> json) => _$ActionFromJson(json);
  Map<String, dynamic> toJson() => _$ActionToJson(this);
}

@JsonSerializable()
class RuleSet {
  final String version;
  @JsonKey(name: 'locale_default')
  final String localeDefault;
  final List<String> profiles;
  final List<Rule> rules;

  const RuleSet({
    required this.version,
    required this.localeDefault,
    required this.profiles,
    required this.rules,
  });

  factory RuleSet.fromJson(Map<String, dynamic> json) => _$RuleSetFromJson(json);
  Map<String, dynamic> toJson() => _$RuleSetToJson(this);
}
