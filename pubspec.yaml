name: dars_language
description: "DARS Language - Facial Gesture Control Application"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI and Material Design
  cupertino_icons: ^1.0.8
  material_color_utilities: ^0.11.1

  # Camera and Face Detection
  camera: ^0.11.0+2
  # MEDIAPIPE HOLISTIC - 543 points de repère !
  http: ^1.1.0
  image: ^4.1.7
  path_provider: ^2.1.1

  # State Management
  provider: ^6.1.2
  riverpod: ^2.5.1
  flutter_riverpod: ^2.5.1

  # JSON and Data Handling
  json_annotation: ^4.9.0
  shared_preferences: ^2.3.2

  # Permissions
  permission_handler: ^11.3.1

  # Platform Integration
  flutter_native_splash: ^2.4.1
  url_launcher: ^6.2.4
  flutter_launcher_icons: ^0.14.1

  # Internationalization
  intl: ^0.20.2

  # File handling
  path: ^1.9.0

  # System integration
  device_info_plus: ^10.1.2
  package_info_plus: ^8.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.4.13
  json_serializable: ^6.8.0

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/
    - assets/images/
    - assets/dars.json

# fonts:
  #   - family: Roboto
  #     fonts:
  #       - asset: fonts/Roboto-Regular.ttf
  #       - asset: fonts/Roboto-Bold.ttf
  #         weight: 700

# Configuration des icônes d'application
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/logo.png"
  adaptive_icon_background: "#000000"
  adaptive_icon_foreground: "assets/images/logo.png"
